import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

class OfferManagementScreen extends StatefulWidget {
  const OfferManagementScreen({Key? key}) : super(key: key);

  @override
  State<OfferManagementScreen> createState() => _OfferManagementScreenState();
}

class _OfferManagementScreenState extends State<OfferManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Mock offers data
  List<PropertyOffer> _offers = [];
  List<PropertyOffer> _filteredOffers = [];
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _offers = [
      PropertyOffer(
        id: '1',
        propertyId: 'prop_1',
        propertyTitle: 'Modern Downtown Condo',
        propertyAddress: '123 Main St, Downtown',
        listingPrice: 650000,
        offerAmount: 620000,
        status: OfferStatus.pending,
        submittedDate: DateTime.now().subtract(const Duration(days: 2)),
        expiryDate: DateTime.now().add(const Duration(days: 5)),
        conditions: ['Financing contingency', 'Home inspection'],
        agentName: 'Sarah Agent',
        agentPhone: '+1234567890',
        notes: 'First offer on this property. Hoping for quick response.',
      ),
      PropertyOffer(
        id: '2',
        propertyId: 'prop_2',
        propertyTitle: 'Family Home with Garden',
        propertyAddress: '456 Oak Ave, Suburbs',
        listingPrice: 850000,
        offerAmount: 825000,
        status: OfferStatus.accepted,
        submittedDate: DateTime.now().subtract(const Duration(days: 10)),
        expiryDate: DateTime.now().subtract(const Duration(days: 3)),
        conditions: ['Home inspection', 'Appraisal contingency'],
        agentName: 'Mike Agent',
        agentPhone: '+1234567891',
        notes: 'Offer accepted! Moving to closing process.',
      ),
      PropertyOffer(
        id: '3',
        propertyId: 'prop_3',
        propertyTitle: 'Waterfront Apartment',
        propertyAddress: '789 Beach Rd, Waterfront',
        listingPrice: 750000,
        offerAmount: 740000,
        status: OfferStatus.rejected,
        submittedDate: DateTime.now().subtract(const Duration(days: 15)),
        expiryDate: DateTime.now().subtract(const Duration(days: 8)),
        conditions: ['Financing contingency'],
        agentName: 'Lisa Agent',
        agentPhone: '+1234567892',
        notes: 'Offer rejected. Seller wants closer to asking price.',
      ),
      PropertyOffer(
        id: '4',
        propertyId: 'prop_4',
        propertyTitle: 'Luxury Penthouse',
        propertyAddress: '321 High St, Uptown',
        listingPrice: 1200000,
        offerAmount: 1150000,
        status: OfferStatus.countered,
        submittedDate: DateTime.now().subtract(const Duration(days: 5)),
        expiryDate: DateTime.now().add(const Duration(days: 2)),
        conditions: ['Home inspection', 'Financing contingency'],
        agentName: 'Tom Agent',
        agentPhone: '+1234567893',
        notes: 'Seller countered at $1,180,000. Considering response.',
        counterOffer: 1180000,
      ),
    ];
    _filteredOffers = List.from(_offers);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Offer Management'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'new_offer',
                child: ListTile(
                  leading: Icon(Icons.add),
                  title: Text('Make New Offer'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Offers'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All Offers'),
            Tab(text: 'Pending'),
            Tab(text: 'Accepted'),
            Tab(text: 'Countered'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildStatsSection(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOffersList(_filteredOffers),
                  _buildOffersList(_filteredOffers.where((o) => o.status == OfferStatus.pending).toList()),
                  _buildOffersList(_filteredOffers.where((o) => o.status == OfferStatus.accepted).toList()),
                  _buildOffersList(_filteredOffers.where((o) => o.status == OfferStatus.countered).toList()),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _makeNewOffer,
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add),
        label: const Text('New Offer'),
      ),
    );
  }

  Widget _buildStatsSection() {
    final totalOffers = _offers.length;
    final pendingOffers = _offers.where((o) => o.status == OfferStatus.pending).length;
    final acceptedOffers = _offers.where((o) => o.status == OfferStatus.accepted).length;
    final averageOffer = _offers.isNotEmpty 
        ? _offers.map((o) => o.offerAmount).reduce((a, b) => a + b) / _offers.length
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard('Total Offers', '$totalOffers', Icons.description, Colors.blue),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Pending', '$pendingOffers', Icons.schedule, Colors.orange),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Accepted', '$acceptedOffers', Icons.check_circle, Colors.green),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Avg Offer', '\$${_formatPrice(averageOffer)}', Icons.trending_up, Colors.purple),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOffersList(List<PropertyOffer> offers) {
    if (offers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No offers found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Start making offers on properties you\'re interested in',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: offers.length,
      itemBuilder: (context, index) {
        final offer = offers[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        offer.propertyTitle,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(offer.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        offer.status.displayName,
                        style: TextStyle(
                          color: _getStatusColor(offer.status),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        offer.propertyAddress,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildPriceInfo('Listing Price', offer.listingPrice, Colors.grey[700]!),
                    ),
                    Expanded(
                      child: _buildPriceInfo('Your Offer', offer.offerAmount, const Color(0xFF4CAF50)),
                    ),
                    if (offer.counterOffer != null)
                      Expanded(
                        child: _buildPriceInfo('Counter Offer', offer.counterOffer!, Colors.orange),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Submitted: ${_formatDate(offer.submittedDate)}',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Expires: ${_formatDate(offer.expiryDate)}',
                      style: TextStyle(
                        color: offer.expiryDate.isBefore(DateTime.now()) ? Colors.red : Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                if (offer.conditions.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: offer.conditions.map((condition) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          condition,
                          style: const TextStyle(
                            color: Colors.blue,
                            fontSize: 12,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
                if (offer.notes.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Text(
                      offer.notes,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (offer.status == OfferStatus.countered) ...[
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _respondToCounter(offer),
                          icon: const Icon(Icons.reply),
                          label: const Text('Respond'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.orange,
                            side: const BorderSide(color: Colors.orange),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                    if (offer.status == OfferStatus.pending) ...[
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _withdrawOffer(offer),
                          icon: const Icon(Icons.cancel),
                          label: const Text('Withdraw'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _viewOfferDetails(offer),
                        icon: const Icon(Icons.visibility),
                        label: const Text('View Details'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4CAF50),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPriceInfo(String label, double amount, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        Text(
          '\$${_formatPrice(amount)}',
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Color _getStatusColor(OfferStatus status) {
    switch (status) {
      case OfferStatus.pending:
        return Colors.orange;
      case OfferStatus.accepted:
        return Colors.green;
      case OfferStatus.rejected:
        return Colors.red;
      case OfferStatus.countered:
        return Colors.blue;
      case OfferStatus.withdrawn:
        return Colors.grey;
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000000) {
      return '${(price / 1000000).toStringAsFixed(1)}M';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(0)}K';
    }
    return price.toStringAsFixed(0);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _makeNewOffer() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Make new offer functionality will be implemented'),
        backgroundColor: Color(0xFF4CAF50),
      ),
    );
  }

  void _viewOfferDetails(PropertyOffer offer) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for offer on ${offer.propertyTitle}'),
        backgroundColor: const Color(0xFF4CAF50),
      ),
    );
  }

  void _respondToCounter(PropertyOffer offer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Respond to Counter Offer'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Property: ${offer.propertyTitle}'),
            const SizedBox(height: 8),
            Text('Your Offer: \$${_formatPrice(offer.offerAmount)}'),
            Text('Counter Offer: \$${_formatPrice(offer.counterOffer!)}'),
            const SizedBox(height: 16),
            const Text('What would you like to do?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Counter offer rejected'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            child: const Text('Reject'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Counter offer accepted!'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
            child: const Text('Accept'),
          ),
        ],
      ),
    );
  }

  void _withdrawOffer(PropertyOffer offer) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Withdraw Offer'),
        content: Text('Are you sure you want to withdraw your offer on "${offer.propertyTitle}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                final index = _offers.indexOf(offer);
                _offers[index] = offer.copyWith(status: OfferStatus.withdrawn);
                _filteredOffers = List.from(_offers);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Offer withdrawn'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Withdraw'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'new_offer':
        _makeNewOffer();
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting offers list...'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
        break;
    }
  }
}

// Offer data models
class PropertyOffer {
  final String id;
  final String propertyId;
  final String propertyTitle;
  final String propertyAddress;
  final double listingPrice;
  final double offerAmount;
  final OfferStatus status;
  final DateTime submittedDate;
  final DateTime expiryDate;
  final List<String> conditions;
  final String agentName;
  final String agentPhone;
  final String notes;
  final double? counterOffer;

  PropertyOffer({
    required this.id,
    required this.propertyId,
    required this.propertyTitle,
    required this.propertyAddress,
    required this.listingPrice,
    required this.offerAmount,
    required this.status,
    required this.submittedDate,
    required this.expiryDate,
    required this.conditions,
    required this.agentName,
    required this.agentPhone,
    required this.notes,
    this.counterOffer,
  });

  PropertyOffer copyWith({
    OfferStatus? status,
    double? counterOffer,
  }) {
    return PropertyOffer(
      id: id,
      propertyId: propertyId,
      propertyTitle: propertyTitle,
      propertyAddress: propertyAddress,
      listingPrice: listingPrice,
      offerAmount: offerAmount,
      status: status ?? this.status,
      submittedDate: submittedDate,
      expiryDate: expiryDate,
      conditions: conditions,
      agentName: agentName,
      agentPhone: agentPhone,
      notes: notes,
      counterOffer: counterOffer ?? this.counterOffer,
    );
  }
}

enum OfferStatus {
  pending,
  accepted,
  rejected,
  countered,
  withdrawn,
}

extension OfferStatusExtension on OfferStatus {
  String get displayName {
    switch (this) {
      case OfferStatus.pending:
        return 'Pending';
      case OfferStatus.accepted:
        return 'Accepted';
      case OfferStatus.rejected:
        return 'Rejected';
      case OfferStatus.countered:
        return 'Countered';
      case OfferStatus.withdrawn:
        return 'Withdrawn';
    }
  }
}
