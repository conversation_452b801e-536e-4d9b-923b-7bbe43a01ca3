import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/property.dart';
import '../../providers/property_provider.dart';

class PropertyModerationScreen extends StatefulWidget {
  const PropertyModerationScreen({Key? key}) : super(key: key);

  @override
  State<PropertyModerationScreen> createState() => _PropertyModerationScreenState();
}

class _PropertyModerationScreenState extends State<PropertyModerationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final _searchController = TextEditingController();
  String _selectedStatus = 'all';
  String _selectedType = 'all';
  bool _isLoading = false;
  
  // Mock moderation data
  List<PropertyModeration> _properties = [];
  List<PropertyModeration> _filteredProperties = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _properties = [
      PropertyModeration(
        property: Property(
          id: '1',
          title: 'Modern Downtown Condo',
          description: 'Beautiful modern condo with city views and premium amenities.',
          price: 650000,
          location: 'Downtown, NY',
          address: '123 Main St',
          type: PropertyType.condo,
          status: PropertyStatus.active,
          bedrooms: 2,
          bathrooms: 2,
          area: 1200,
          images: [],
          latitude: 40.7128,
          longitude: -74.0060,
          agentName: 'Sarah Agent',
          agentPhone: '+1234567890',
          agentEmail: '<EMAIL>',
          amenities: ['Swimming Pool', 'Gym', 'Concierge'],
          ownerId: 'owner1',
          ownerName: 'John Owner',
          ownerEmail: '<EMAIL>',
          ownerPhone: '+1234567890',
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
        moderationStatus: ModerationStatus.pending,
        submittedDate: DateTime.now().subtract(const Duration(days: 2)),
        reviewedDate: null,
        reviewedBy: null,
        flags: ['Incomplete description'],
        priority: ModerationPriority.medium,
        notes: 'Property needs more detailed description and additional photos.',
      ),
      PropertyModeration(
        property: Property(
          id: '2',
          title: 'Luxury Penthouse Suite',
          description: 'Exclusive penthouse with panoramic city views.',
          price: 2500000,
          location: 'Uptown, NY',
          address: '456 Elite Ave',
          type: PropertyType.apartment,
          status: PropertyStatus.active,
          bedrooms: 4,
          bathrooms: 3,
          area: 3500,
          images: [],
          latitude: 40.7831,
          longitude: -73.9712,
          agentName: 'Mike Agent',
          agentPhone: '+1234567891',
          agentEmail: '<EMAIL>',
          amenities: ['Private Elevator', 'Rooftop Terrace', 'Wine Cellar'],
          ownerId: 'owner2',
          ownerName: 'Jane Owner',
          ownerEmail: '<EMAIL>',
          ownerPhone: '+1234567891',
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
          updatedAt: DateTime.now(),
        ),
        moderationStatus: ModerationStatus.flagged,
        submittedDate: DateTime.now().subtract(const Duration(hours: 12)),
        reviewedDate: null,
        reviewedBy: null,
        flags: ['Suspicious pricing', 'Unverified photos'],
        priority: ModerationPriority.high,
        notes: 'Price seems unusually high for the area. Photos need verification.',
      ),
      PropertyModeration(
        property: Property(
          id: '3',
          title: 'Family Home with Garden',
          description: 'Spacious family home with large garden and garage.',
          price: 850000,
          location: 'Suburbs, NY',
          address: '789 Oak Ave',
          type: PropertyType.house,
          status: PropertyStatus.active,
          bedrooms: 4,
          bathrooms: 3,
          area: 2500,
          images: [],
          latitude: 40.7589,
          longitude: -73.9851,
          agentName: 'Lisa Agent',
          agentPhone: '+1234567892',
          agentEmail: '<EMAIL>',
          amenities: ['Garden', 'Garage', 'Fireplace'],
          ownerId: 'owner3',
          ownerName: 'Bob Owner',
          ownerEmail: '<EMAIL>',
          ownerPhone: '+1234567892',
          createdAt: DateTime.now().subtract(const Duration(days: 7)),
          updatedAt: DateTime.now().subtract(const Duration(days: 3)),
        ),
        moderationStatus: ModerationStatus.approved,
        submittedDate: DateTime.now().subtract(const Duration(days: 7)),
        reviewedDate: DateTime.now().subtract(const Duration(days: 5)),
        reviewedBy: 'Admin User',
        flags: [],
        priority: ModerationPriority.low,
        notes: 'Property approved after verification.',
      ),
    ];
    _filteredProperties = List.from(_properties);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Property Moderation'),
        backgroundColor: const Color(0xFF9C27B0),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'bulk_approve',
                child: ListTile(
                  leading: Icon(Icons.check_circle, color: Colors.green),
                  title: Text('Bulk Approve'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'bulk_reject',
                child: ListTile(
                  leading: Icon(Icons.cancel, color: Colors.red),
                  title: Text('Bulk Reject'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export_report',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Report'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All Properties'),
            Tab(text: 'Pending'),
            Tab(text: 'Flagged'),
            Tab(text: 'Approved'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildStatsSection(),
            _buildFiltersSection(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPropertiesList(_filteredProperties),
                  _buildPropertiesList(_filteredProperties.where((p) => p.moderationStatus == ModerationStatus.pending).toList()),
                  _buildPropertiesList(_filteredProperties.where((p) => p.moderationStatus == ModerationStatus.flagged).toList()),
                  _buildPropertiesList(_filteredProperties.where((p) => p.moderationStatus == ModerationStatus.approved).toList()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    final totalProperties = _properties.length;
    final pendingProperties = _properties.where((p) => p.moderationStatus == ModerationStatus.pending).length;
    final flaggedProperties = _properties.where((p) => p.moderationStatus == ModerationStatus.flagged).length;
    final approvedProperties = _properties.where((p) => p.moderationStatus == ModerationStatus.approved).length;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard('Total', '$totalProperties', Icons.home, Colors.blue),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Pending', '$pendingProperties', Icons.schedule, Colors.orange),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Flagged', '$flaggedProperties', Icons.flag, Colors.red),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Approved', '$approvedProperties', Icons.check_circle, Colors.green),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search properties by title, location, or owner...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF9C27B0)),
              ),
            ),
            onChanged: _filterProperties,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Status')),
                    DropdownMenuItem(value: 'pending', child: Text('Pending')),
                    DropdownMenuItem(value: 'flagged', child: Text('Flagged')),
                    DropdownMenuItem(value: 'approved', child: Text('Approved')),
                    DropdownMenuItem(value: 'rejected', child: Text('Rejected')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedStatus = value!);
                    _filterProperties(_searchController.text);
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedType,
                  decoration: InputDecoration(
                    labelText: 'Type',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Types')),
                    DropdownMenuItem(value: 'house', child: Text('Houses')),
                    DropdownMenuItem(value: 'apartment', child: Text('Apartments')),
                    DropdownMenuItem(value: 'condo', child: Text('Condos')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedType = value!);
                    _filterProperties(_searchController.text);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPropertiesList(List<PropertyModeration> properties) {
    if (properties.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.home_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No properties found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: properties.length,
      itemBuilder: (context, index) {
        final propertyMod = properties[index];
        final property = propertyMod.property;

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(propertyMod.priority).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        propertyMod.priority.displayName,
                        style: TextStyle(
                          color: _getPriorityColor(propertyMod.priority),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(propertyMod.moderationStatus).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        propertyMod.moderationStatus.displayName,
                        style: TextStyle(
                          color: _getStatusColor(propertyMod.moderationStatus),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  property.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        property.location,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Text(
                      '\$${_formatPrice(property.price)}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Text(
                      property.type.displayName,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.person, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Owner: ${property.ownerName}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.real_estate_agent, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Agent: ${property.agentName}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                if (propertyMod.flags.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: propertyMod.flags.map((flag) {
                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          flag,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
                if (propertyMod.notes.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Text(
                      propertyMod.notes,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  children: [
                    Text(
                      'Submitted: ${_formatDate(propertyMod.submittedDate)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                    if (propertyMod.reviewedDate != null) ...[
                      const SizedBox(width: 16),
                      Text(
                        'Reviewed: ${_formatDate(propertyMod.reviewedDate!)}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (propertyMod.moderationStatus == ModerationStatus.pending ||
                        propertyMod.moderationStatus == ModerationStatus.flagged) ...[
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _rejectProperty(propertyMod),
                          icon: const Icon(Icons.cancel),
                          label: const Text('Reject'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _approveProperty(propertyMod),
                          icon: const Icon(Icons.check_circle),
                          label: const Text('Approve'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ] else ...[
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _viewPropertyDetails(property),
                          icon: const Icon(Icons.visibility),
                          label: const Text('View Details'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF9C27B0),
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getPriorityColor(ModerationPriority priority) {
    switch (priority) {
      case ModerationPriority.high:
        return Colors.red;
      case ModerationPriority.medium:
        return Colors.orange;
      case ModerationPriority.low:
        return Colors.green;
    }
  }

  Color _getStatusColor(ModerationStatus status) {
    switch (status) {
      case ModerationStatus.pending:
        return Colors.orange;
      case ModerationStatus.flagged:
        return Colors.red;
      case ModerationStatus.approved:
        return Colors.green;
      case ModerationStatus.rejected:
        return Colors.grey;
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000000) {
      return '${(price / 1000000).toStringAsFixed(1)}M';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(0)}K';
    }
    return price.toStringAsFixed(0);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _filterProperties(String query) {
    setState(() {
      _filteredProperties = _properties.where((propertyMod) {
        final property = propertyMod.property;
        final matchesSearch = query.isEmpty ||
            property.title.toLowerCase().contains(query.toLowerCase()) ||
            property.location.toLowerCase().contains(query.toLowerCase()) ||
            property.ownerName.toLowerCase().contains(query.toLowerCase());

        final matchesStatus = _selectedStatus == 'all' ||
            propertyMod.moderationStatus.toString().split('.').last == _selectedStatus;

        final matchesType = _selectedType == 'all' ||
            property.type.toString().split('.').last == _selectedType;

        return matchesSearch && matchesStatus && matchesType;
      }).toList();
    });
  }

  void _approveProperty(PropertyModeration propertyMod) {
    setState(() {
      final index = _properties.indexOf(propertyMod);
      _properties[index] = propertyMod.copyWith(
        moderationStatus: ModerationStatus.approved,
        reviewedDate: DateTime.now(),
        reviewedBy: 'Current Admin',
      );
      _filteredProperties = List.from(_properties);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${propertyMod.property.title} has been approved'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _rejectProperty(PropertyModeration propertyMod) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Property'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Are you sure you want to reject "${propertyMod.property.title}"?'),
            const SizedBox(height: 16),
            TextField(
              decoration: const InputDecoration(
                labelText: 'Rejection reason',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                final index = _properties.indexOf(propertyMod);
                _properties[index] = propertyMod.copyWith(
                  moderationStatus: ModerationStatus.rejected,
                  reviewedDate: DateTime.now(),
                  reviewedBy: 'Current Admin',
                );
                _filteredProperties = List.from(_properties);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${propertyMod.property.title} has been rejected'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _viewPropertyDetails(Property property) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening details for ${property.title}'),
        backgroundColor: const Color(0xFF9C27B0),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'bulk_approve':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bulk approve functionality will be implemented'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'bulk_reject':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bulk reject functionality will be implemented'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'export_report':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting moderation report...'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
    }
  }
}

// Property moderation data models
class PropertyModeration {
  final Property property;
  final ModerationStatus moderationStatus;
  final DateTime submittedDate;
  final DateTime? reviewedDate;
  final String? reviewedBy;
  final List<String> flags;
  final ModerationPriority priority;
  final String notes;

  PropertyModeration({
    required this.property,
    required this.moderationStatus,
    required this.submittedDate,
    this.reviewedDate,
    this.reviewedBy,
    required this.flags,
    required this.priority,
    required this.notes,
  });

  PropertyModeration copyWith({
    ModerationStatus? moderationStatus,
    DateTime? reviewedDate,
    String? reviewedBy,
    List<String>? flags,
    String? notes,
  }) {
    return PropertyModeration(
      property: property,
      moderationStatus: moderationStatus ?? this.moderationStatus,
      submittedDate: submittedDate,
      reviewedDate: reviewedDate ?? this.reviewedDate,
      reviewedBy: reviewedBy ?? this.reviewedBy,
      flags: flags ?? this.flags,
      priority: priority,
      notes: notes ?? this.notes,
    );
  }
}

enum ModerationStatus {
  pending,
  flagged,
  approved,
  rejected,
}

enum ModerationPriority {
  high,
  medium,
  low,
}

extension ModerationStatusExtension on ModerationStatus {
  String get displayName {
    switch (this) {
      case ModerationStatus.pending:
        return 'Pending';
      case ModerationStatus.flagged:
        return 'Flagged';
      case ModerationStatus.approved:
        return 'Approved';
      case ModerationStatus.rejected:
        return 'Rejected';
    }
  }
}

extension ModerationPriorityExtension on ModerationPriority {
  String get displayName {
    switch (this) {
      case ModerationPriority.high:
        return 'High';
      case ModerationPriority.medium:
        return 'Medium';
      case ModerationPriority.low:
        return 'Low';
    }
  }
}
