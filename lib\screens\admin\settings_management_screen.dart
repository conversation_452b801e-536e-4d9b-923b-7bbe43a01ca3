import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class SettingsManagementScreen extends StatefulWidget {
  const SettingsManagementScreen({Key? key}) : super(key: key);

  @override
  State<SettingsManagementScreen> createState() => _SettingsManagementScreenState();
}

class _SettingsManagementScreenState extends State<SettingsManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Settings state
  Map<String, dynamic> _systemSettings = {};
  Map<String, dynamic> _securitySettings = {};
  Map<String, dynamic> _notificationSettings = {};
  Map<String, dynamic> _integrationSettings = {};
  
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeSettings();
    _animationController.forward();
  }

  void _initializeSettings() {
    _systemSettings = {
      'maintenance_mode': false,
      'registration_enabled': true,
      'property_auto_approval': false,
      'max_properties_per_user': 10,
      'session_timeout': 30,
      'backup_frequency': 'daily',
      'log_retention_days': 90,
    };

    _securitySettings = {
      'two_factor_required': false,
      'password_min_length': 8,
      'password_require_special': true,
      'login_attempts_limit': 5,
      'account_lockout_duration': 15,
      'ip_whitelist_enabled': false,
      'ssl_required': true,
    };

    _notificationSettings = {
      'email_notifications': true,
      'sms_notifications': false,
      'push_notifications': true,
      'admin_alerts': true,
      'user_welcome_email': true,
      'property_approval_email': true,
      'transaction_notifications': true,
    };

    _integrationSettings = {
      'google_maps_enabled': true,
      'payment_gateway_enabled': true,
      'social_login_enabled': true,
      'analytics_enabled': true,
      'backup_cloud_enabled': true,
      'email_service_provider': 'sendgrid',
      'sms_service_provider': 'twilio',
    };
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings Management'),
        backgroundColor: const Color(0xFF9C27B0),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _saveAllSettings,
            icon: const Icon(Icons.save),
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_config',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Configuration'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'import_config',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('Import Configuration'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'reset_defaults',
                child: ListTile(
                  leading: Icon(Icons.restore, color: Colors.red),
                  title: Text('Reset to Defaults', style: TextStyle(color: Colors.red)),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'System'),
            Tab(text: 'Security'),
            Tab(text: 'Notifications'),
            Tab(text: 'Integrations'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildSystemSettingsTab(),
            _buildSecuritySettingsTab(),
            _buildNotificationSettingsTab(),
            _buildIntegrationSettingsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('General Settings'),
          const SizedBox(height: 16),
          _buildSwitchSetting(
            'Maintenance Mode',
            'Enable maintenance mode to restrict access',
            _systemSettings['maintenance_mode'],
            (value) => _updateSetting('system', 'maintenance_mode', value),
          ),
          _buildSwitchSetting(
            'User Registration',
            'Allow new users to register accounts',
            _systemSettings['registration_enabled'],
            (value) => _updateSetting('system', 'registration_enabled', value),
          ),
          _buildSwitchSetting(
            'Auto-approve Properties',
            'Automatically approve new property listings',
            _systemSettings['property_auto_approval'],
            (value) => _updateSetting('system', 'property_auto_approval', value),
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Limits & Quotas'),
          const SizedBox(height: 16),
          _buildNumberSetting(
            'Max Properties per User',
            'Maximum number of properties a user can list',
            _systemSettings['max_properties_per_user'],
            (value) => _updateSetting('system', 'max_properties_per_user', value),
          ),
          _buildNumberSetting(
            'Session Timeout (minutes)',
            'User session timeout duration',
            _systemSettings['session_timeout'],
            (value) => _updateSetting('system', 'session_timeout', value),
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Maintenance'),
          const SizedBox(height: 16),
          _buildDropdownSetting(
            'Backup Frequency',
            'How often to perform system backups',
            _systemSettings['backup_frequency'],
            ['daily', 'weekly', 'monthly'],
            (value) => _updateSetting('system', 'backup_frequency', value),
          ),
          _buildNumberSetting(
            'Log Retention (days)',
            'How long to keep system logs',
            _systemSettings['log_retention_days'],
            (value) => _updateSetting('system', 'log_retention_days', value),
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Authentication'),
          const SizedBox(height: 16),
          _buildSwitchSetting(
            'Require Two-Factor Authentication',
            'Force all users to enable 2FA',
            _securitySettings['two_factor_required'],
            (value) => _updateSetting('security', 'two_factor_required', value),
          ),
          _buildSwitchSetting(
            'Require SSL',
            'Force HTTPS connections',
            _securitySettings['ssl_required'],
            (value) => _updateSetting('security', 'ssl_required', value),
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Password Policy'),
          const SizedBox(height: 16),
          _buildNumberSetting(
            'Minimum Password Length',
            'Minimum number of characters required',
            _securitySettings['password_min_length'],
            (value) => _updateSetting('security', 'password_min_length', value),
          ),
          _buildSwitchSetting(
            'Require Special Characters',
            'Passwords must contain special characters',
            _securitySettings['password_require_special'],
            (value) => _updateSetting('security', 'password_require_special', value),
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Account Security'),
          const SizedBox(height: 16),
          _buildNumberSetting(
            'Login Attempts Limit',
            'Maximum failed login attempts before lockout',
            _securitySettings['login_attempts_limit'],
            (value) => _updateSetting('security', 'login_attempts_limit', value),
          ),
          _buildNumberSetting(
            'Account Lockout Duration (minutes)',
            'How long to lock accounts after failed attempts',
            _securitySettings['account_lockout_duration'],
            (value) => _updateSetting('security', 'account_lockout_duration', value),
          ),
          _buildSwitchSetting(
            'IP Whitelist',
            'Enable IP address whitelisting for admin access',
            _securitySettings['ip_whitelist_enabled'],
            (value) => _updateSetting('security', 'ip_whitelist_enabled', value),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Notification Channels'),
          const SizedBox(height: 16),
          _buildSwitchSetting(
            'Email Notifications',
            'Enable email notifications system-wide',
            _notificationSettings['email_notifications'],
            (value) => _updateSetting('notifications', 'email_notifications', value),
          ),
          _buildSwitchSetting(
            'SMS Notifications',
            'Enable SMS notifications system-wide',
            _notificationSettings['sms_notifications'],
            (value) => _updateSetting('notifications', 'sms_notifications', value),
          ),
          _buildSwitchSetting(
            'Push Notifications',
            'Enable push notifications for mobile apps',
            _notificationSettings['push_notifications'],
            (value) => _updateSetting('notifications', 'push_notifications', value),
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Automated Notifications'),
          const SizedBox(height: 16),
          _buildSwitchSetting(
            'Admin Alerts',
            'Send alerts to administrators for important events',
            _notificationSettings['admin_alerts'],
            (value) => _updateSetting('notifications', 'admin_alerts', value),
          ),
          _buildSwitchSetting(
            'User Welcome Email',
            'Send welcome email to new users',
            _notificationSettings['user_welcome_email'],
            (value) => _updateSetting('notifications', 'user_welcome_email', value),
          ),
          _buildSwitchSetting(
            'Property Approval Email',
            'Notify users when properties are approved/rejected',
            _notificationSettings['property_approval_email'],
            (value) => _updateSetting('notifications', 'property_approval_email', value),
          ),
          _buildSwitchSetting(
            'Transaction Notifications',
            'Send notifications for transaction updates',
            _notificationSettings['transaction_notifications'],
            (value) => _updateSetting('notifications', 'transaction_notifications', value),
          ),
        ],
      ),
    );
  }

  Widget _buildIntegrationSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('External Services'),
          const SizedBox(height: 16),
          _buildSwitchSetting(
            'Google Maps Integration',
            'Enable Google Maps for property locations',
            _integrationSettings['google_maps_enabled'],
            (value) => _updateSetting('integrations', 'google_maps_enabled', value),
          ),
          _buildSwitchSetting(
            'Payment Gateway',
            'Enable payment processing',
            _integrationSettings['payment_gateway_enabled'],
            (value) => _updateSetting('integrations', 'payment_gateway_enabled', value),
          ),
          _buildSwitchSetting(
            'Social Login',
            'Allow login with social media accounts',
            _integrationSettings['social_login_enabled'],
            (value) => _updateSetting('integrations', 'social_login_enabled', value),
          ),
          _buildSwitchSetting(
            'Analytics Tracking',
            'Enable user behavior analytics',
            _integrationSettings['analytics_enabled'],
            (value) => _updateSetting('integrations', 'analytics_enabled', value),
          ),
          _buildSwitchSetting(
            'Cloud Backup',
            'Enable automatic cloud backups',
            _integrationSettings['backup_cloud_enabled'],
            (value) => _updateSetting('integrations', 'backup_cloud_enabled', value),
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Service Providers'),
          const SizedBox(height: 16),
          _buildDropdownSetting(
            'Email Service Provider',
            'Choose email service for notifications',
            _integrationSettings['email_service_provider'],
            ['sendgrid', 'mailgun', 'ses', 'smtp'],
            (value) => _updateSetting('integrations', 'email_service_provider', value),
          ),
          _buildDropdownSetting(
            'SMS Service Provider',
            'Choose SMS service for notifications',
            _integrationSettings['sms_service_provider'],
            ['twilio', 'nexmo', 'aws_sns'],
            (value) => _updateSetting('integrations', 'sms_service_provider', value),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xFF9C27B0),
      ),
    );
  }

  Widget _buildSwitchSetting(String title, String description, bool value, Function(bool) onChanged) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: value,
              onChanged: onChanged,
              activeColor: const Color(0xFF9C27B0),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNumberSetting(String title, String description, int value, Function(int) onChanged) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            TextFormField(
              initialValue: value.toString(),
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF9C27B0)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              onChanged: (newValue) {
                final intValue = int.tryParse(newValue);
                if (intValue != null) {
                  onChanged(intValue);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownSetting(String title, String description, String value, List<String> options, Function(String) onChanged) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: value,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF9C27B0)),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: options.map((option) {
                return DropdownMenuItem(
                  value: option,
                  child: Text(option.toUpperCase()),
                );
              }).toList(),
              onChanged: (newValue) {
                if (newValue != null) {
                  onChanged(newValue);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _updateSetting(String category, String key, dynamic value) {
    setState(() {
      switch (category) {
        case 'system':
          _systemSettings[key] = value;
          break;
        case 'security':
          _securitySettings[key] = value;
          break;
        case 'notifications':
          _notificationSettings[key] = value;
          break;
        case 'integrations':
          _integrationSettings[key] = value;
          break;
      }
    });
  }

  void _saveAllSettings() {
    setState(() {
      _isLoading = true;
    });

    // Simulate saving settings
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
      }
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_config':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting configuration...'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'import_config':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Import configuration functionality will be implemented'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'reset_defaults':
        _showResetConfirmation();
        break;
    }
  }

  void _showResetConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset to Defaults'),
        content: const Text(
          'Are you sure you want to reset all settings to their default values? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _resetToDefaults();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  void _resetToDefaults() {
    setState(() {
      _initializeSettings();
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Settings reset to defaults'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
