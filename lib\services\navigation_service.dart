import 'package:flutter/material.dart';
import '../models/user.dart';

class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  static NavigatorState? get navigator => navigatorKey.currentState;
  static BuildContext? get context => navigatorKey.currentContext;

  // Route names
  static const String splash = '/';
  static const String auth = '/auth';
  static const String dashboard = '/dashboard';
  static const String sellerDashboard = '/seller-dashboard';
  static const String buyerDashboard = '/buyer-dashboard';
  static const String agentDashboard = '/agent-dashboard';
  static const String adminDashboard = '/admin-dashboard';
  static const String listings = '/listings';
  static const String propertyDetail = '/property-detail';
  static const String favorites = '/favorites';
  static const String profile = '/profile';
  static const String addProperty = '/add-property';
  static const String editProperty = '/edit-property';
  static const String propertyAnalytics = '/property-analytics';
  static const String inquiries = '/inquiries';

  // Navigation methods
  static Future<void> navigateToSplash() async {
    await navigator?.pushNamedAndRemoveUntil(splash, (route) => false);
  }

  static Future<void> navigateToAuth() async {
    await navigator?.pushNamedAndRemoveUntil(auth, (route) => false);
  }

  static Future<void> navigateToRoleDashboard(UserRole role) async {
    String route;
    switch (role) {
      case UserRole.seller:
        route = sellerDashboard;
        break;
      case UserRole.buyer:
        route = buyerDashboard;
        break;
      case UserRole.agent:
        route = agentDashboard;
        break;
      case UserRole.admin:
        route = adminDashboard;
        break;
      default:
        route = buyerDashboard; // Default fallback
    }
    
    print('NavigationService: Navigating to $route for role $role');
    await navigator?.pushNamedAndRemoveUntil(route, (route) => false);
  }

  static Future<void> navigateToGenericDashboard() async {
    await navigator?.pushNamedAndRemoveUntil(dashboard, (route) => false);
  }

  static Future<void> navigateToListings() async {
    await navigator?.pushNamed(listings);
  }

  static Future<void> navigateToPropertyDetail(String propertyId) async {
    await navigator?.pushNamed(propertyDetail, arguments: propertyId);
  }

  static Future<void> navigateToFavorites() async {
    await navigator?.pushNamed(favorites);
  }

  static Future<void> navigateToProfile() async {
    await navigator?.pushNamed(profile);
  }

  static Future<void> navigateToAddProperty() async {
    await navigator?.pushNamed(addProperty);
  }

  static Future<void> navigateToEditProperty(String propertyId) async {
    await navigator?.pushNamed(editProperty, arguments: propertyId);
  }

  static Future<void> navigateToPropertyAnalytics(String propertyId) async {
    await navigator?.pushNamed(propertyAnalytics, arguments: propertyId);
  }

  static Future<void> navigateToInquiries() async {
    await navigator?.pushNamed(inquiries);
  }

  // Navigation with replacement
  static Future<void> navigateAndReplace(String routeName, {Object? arguments}) async {
    await navigator?.pushReplacementNamed(routeName, arguments: arguments);
  }

  // Navigation with clearing stack
  static Future<void> navigateAndClearStack(String routeName, {Object? arguments}) async {
    await navigator?.pushNamedAndRemoveUntil(routeName, (route) => false, arguments: arguments);
  }

  // Pop navigation
  static void pop([Object? result]) {
    navigator?.pop(result);
  }

  // Pop until specific route
  static void popUntil(String routeName) {
    navigator?.popUntil(ModalRoute.withName(routeName));
  }

  // Check if can pop
  static bool canPop() {
    return navigator?.canPop() ?? false;
  }

  // Handle authentication state changes
  static Future<void> handleAuthStateChange({
    required bool isAuthenticated,
    required bool isLoading,
    User? user,
  }) async {
    print('NavigationService: Handling auth state change - isAuthenticated: $isAuthenticated, isLoading: $isLoading, user: ${user?.name}');
    
    if (isLoading) {
      // Don't navigate while loading
      return;
    }

    if (!isAuthenticated || user == null) {
      // User is not authenticated, navigate to auth
      await navigateToAuth();
    } else {
      // User is authenticated, navigate to appropriate dashboard
      await navigateToRoleDashboard(user.role);
    }
  }

  // Handle registration completion
  static Future<void> handleRegistrationComplete(User user) async {
    print('NavigationService: Registration complete for user ${user.name} with role ${user.role}');
    
    // Small delay to ensure all state is updated
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Navigate to the appropriate dashboard
    await navigateToRoleDashboard(user.role);
  }

  // Handle login completion
  static Future<void> handleLoginComplete(User user) async {
    print('NavigationService: Login complete for user ${user.name} with role ${user.role}');
    
    // Small delay to ensure all state is updated
    await Future.delayed(const Duration(milliseconds: 300));
    
    // Navigate to the appropriate dashboard
    await navigateToRoleDashboard(user.role);
  }

  // Handle logout
  static Future<void> handleLogout() async {
    print('NavigationService: Handling logout');
    await navigateToAuth();
  }

  // Error navigation
  static Future<void> navigateToError(String error) async {
    // For now, just show a snackbar and navigate to auth
    if (context != null) {
      ScaffoldMessenger.of(context!).showSnackBar(
        SnackBar(
          content: Text(error),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
    await navigateToAuth();
  }

  // Utility method to get current route name
  static String? getCurrentRouteName() {
    return ModalRoute.of(context!)?.settings.name;
  }

  // Check if currently on a specific route
  static bool isCurrentRoute(String routeName) {
    return getCurrentRouteName() == routeName;
  }

  // Navigate with custom transition
  static Future<void> navigateWithTransition(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) async {
    await navigator?.push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => page,
        transitionDuration: duration,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(1.0, 0.0),
              end: Offset.zero,
            ).animate(CurvedAnimation(parent: animation, curve: curve)),
            child: child,
          );
        },
      ),
    );
  }

  // Show modal bottom sheet
  static Future<T?> showModalSheet<T>(Widget child) async {
    return await showModalBottomSheet<T>(
      context: context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => child,
    );
  }

  // Show dialog
  static Future<T?> showAppDialog<T>(Widget dialog) async {
    return await showDialog<T>(
      context: context!,
      builder: (context) => dialog,
    );
  }

  // Show confirmation dialog
  static Future<bool> showConfirmationDialog({
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    final result = await showAppDialog<bool>(
      AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => navigator?.pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => navigator?.pop(true),
            child: Text(confirmText),
          ),
        ],
      ),
    );
    return result ?? false;
  }
}
