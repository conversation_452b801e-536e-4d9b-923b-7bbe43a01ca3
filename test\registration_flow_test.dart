import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:real_estate/main.dart';
import 'package:real_estate/models/user.dart';
import 'package:real_estate/providers/user_provider.dart';
import 'package:real_estate/providers/app_provider.dart';
import 'package:real_estate/providers/property_provider.dart';
import 'package:real_estate/providers/dashboard_provider.dart';
import 'package:real_estate/services/navigation_service.dart';
import 'package:real_estate/auth/enhanced_register_screen.dart';
import 'package:real_estate/auth/multi_step_register_screen.dart';
import 'package:real_estate/dashboards/buyer_dashboard.dart';
import 'package:real_estate/dashboards/seller_dashboard.dart';
import 'package:real_estate/dashboards/agent_dashboard.dart';
import 'package:real_estate/dashboards/admin_dashboard.dart';

// Generate mocks
@GenerateMocks([UserProvider, AppProvider, PropertyProvider, DashboardProvider])
import 'registration_flow_test.mocks.dart';

void main() {
  group('Registration and Navigation Flow Tests', () {
    late MockUserProvider mockUserProvider;
    late MockAppProvider mockAppProvider;
    late MockPropertyProvider mockPropertyProvider;
    late MockDashboardProvider mockDashboardProvider;

    setUp(() {
      mockUserProvider = MockUserProvider();
      mockAppProvider = MockAppProvider();
      mockPropertyProvider = MockPropertyProvider();
      mockDashboardProvider = MockDashboardProvider();
    });

    Widget createTestApp({
      bool isAuthenticated = false,
      User? currentUser,
      bool isLoading = false,
    }) {
      // Setup mock behavior
      when(mockUserProvider.isAuthenticated).thenReturn(isAuthenticated);
      when(mockUserProvider.currentUser).thenReturn(currentUser);
      when(mockUserProvider.isLoading).thenReturn(isLoading);
      when(mockAppProvider.themeMode).thenReturn(ThemeMode.light);

      return MultiProvider(
        providers: [
          ChangeNotifierProvider<UserProvider>.value(value: mockUserProvider),
          ChangeNotifierProvider<AppProvider>.value(value: mockAppProvider),
          ChangeNotifierProvider<PropertyProvider>.value(value: mockPropertyProvider),
          ChangeNotifierProvider<DashboardProvider>.value(value: mockDashboardProvider),
        ],
        child: MaterialApp(
          navigatorKey: NavigationService.navigatorKey,
          home: const Scaffold(body: Text('Test App')),
          routes: {
            '/auth': (context) => const Scaffold(body: Text('Auth Screen')),
            '/buyer-dashboard': (context) => const BuyerDashboard(),
            '/seller-dashboard': (context) => const SellerDashboard(),
            '/agent-dashboard': (context) => const AgentDashboard(),
            '/admin-dashboard': (context) => const AdminDashboard(),
          },
        ),
      );
    }

    testWidgets('Navigation service navigates to correct dashboard for buyer', (WidgetTester tester) async {
      final buyerUser = User(
        id: 'test-buyer-id',
        name: 'Test Buyer',
        email: '<EMAIL>',
        phone: '**********',
        role: UserRole.buyer,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        favoritePropertyIds: [],
        preferences: UserPreferences.initial(),
      );

      await tester.pumpWidget(createTestApp(
        isAuthenticated: true,
        currentUser: buyerUser,
      ));

      // Test navigation to buyer dashboard
      await NavigationService.navigateToRoleDashboard(UserRole.buyer);
      await tester.pumpAndSettle();

      // Verify we're on the buyer dashboard route
      expect(NavigationService.getCurrentRouteName(), equals('/buyer-dashboard'));
    });

    testWidgets('Navigation service navigates to correct dashboard for seller', (WidgetTester tester) async {
      final sellerUser = User(
        id: 'test-seller-id',
        name: 'Test Seller',
        email: '<EMAIL>',
        phone: '**********',
        role: UserRole.seller,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        favoritePropertyIds: [],
        preferences: UserPreferences.initial(),
      );

      await tester.pumpWidget(createTestApp(
        isAuthenticated: true,
        currentUser: sellerUser,
      ));

      // Test navigation to seller dashboard
      await NavigationService.navigateToRoleDashboard(UserRole.seller);
      await tester.pumpAndSettle();

      // Verify we're on the seller dashboard route
      expect(NavigationService.getCurrentRouteName(), equals('/seller-dashboard'));
    });

    testWidgets('Navigation service navigates to correct dashboard for agent', (WidgetTester tester) async {
      final agentUser = User(
        id: 'test-agent-id',
        name: 'Test Agent',
        email: '<EMAIL>',
        phone: '**********',
        role: UserRole.agent,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        favoritePropertyIds: [],
        preferences: UserPreferences.initial(),
      );

      await tester.pumpWidget(createTestApp(
        isAuthenticated: true,
        currentUser: agentUser,
      ));

      // Test navigation to agent dashboard
      await NavigationService.navigateToRoleDashboard(UserRole.agent);
      await tester.pumpAndSettle();

      // Verify we're on the agent dashboard route
      expect(NavigationService.getCurrentRouteName(), equals('/agent-dashboard'));
    });

    testWidgets('Navigation service navigates to correct dashboard for admin', (WidgetTester tester) async {
      final adminUser = User(
        id: 'test-admin-id',
        name: 'Test Admin',
        email: '<EMAIL>',
        phone: '**********',
        role: UserRole.admin,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        favoritePropertyIds: [],
        preferences: UserPreferences.initial(),
      );

      await tester.pumpWidget(createTestApp(
        isAuthenticated: true,
        currentUser: adminUser,
      ));

      // Test navigation to admin dashboard
      await NavigationService.navigateToRoleDashboard(UserRole.admin);
      await tester.pumpAndSettle();

      // Verify we're on the admin dashboard route
      expect(NavigationService.getCurrentRouteName(), equals('/admin-dashboard'));
    });

    testWidgets('Registration completion triggers correct navigation', (WidgetTester tester) async {
      final buyerUser = User(
        id: 'test-buyer-id',
        name: 'Test Buyer',
        email: '<EMAIL>',
        phone: '**********',
        role: UserRole.buyer,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        favoritePropertyIds: [],
        preferences: UserPreferences.initial(),
      );

      await tester.pumpWidget(createTestApp());

      // Simulate successful registration
      await NavigationService.handleRegistrationComplete(buyerUser);
      await tester.pumpAndSettle();

      // Verify navigation to buyer dashboard
      expect(NavigationService.getCurrentRouteName(), equals('/buyer-dashboard'));
    });

    testWidgets('Multi-step registration screen displays correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<UserProvider>.value(value: mockUserProvider),
            ChangeNotifierProvider<AppProvider>.value(value: mockAppProvider),
            ChangeNotifierProvider<PropertyProvider>.value(value: mockPropertyProvider),
            ChangeNotifierProvider<DashboardProvider>.value(value: mockDashboardProvider),
          ],
          child: const MaterialApp(
            home: MultiStepRegisterScreen(),
          ),
        ),
      );

      // Verify the first step is displayed
      expect(find.text('Basic Information'), findsOneWidget);
      expect(find.text('Let\'s start with your basic information'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(3)); // Name, Email, Phone

      // Verify progress indicator
      expect(find.text('1'), findsOneWidget);
      expect(find.text('2'), findsOneWidget);
      expect(find.text('3'), findsOneWidget);
    });

    testWidgets('Multi-step registration progresses through steps', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<UserProvider>.value(value: mockUserProvider),
            ChangeNotifierProvider<AppProvider>.value(value: mockAppProvider),
            ChangeNotifierProvider<PropertyProvider>.value(value: mockPropertyProvider),
            ChangeNotifierProvider<DashboardProvider>.value(value: mockDashboardProvider),
          ],
          child: const MaterialApp(
            home: MultiStepRegisterScreen(),
          ),
        ),
      );

      // Fill in first step
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(2), '**********');

      // Tap Next button
      await tester.tap(find.text('Next'));
      await tester.pumpAndSettle();

      // Verify we're on step 2
      expect(find.text('Secure your account'), findsOneWidget);
      expect(find.text('Create a strong password to protect your account'), findsOneWidget);
    });

    testWidgets('Role selection works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<UserProvider>.value(value: mockUserProvider),
            ChangeNotifierProvider<AppProvider>.value(value: mockAppProvider),
            ChangeNotifierProvider<PropertyProvider>.value(value: mockPropertyProvider),
            ChangeNotifierProvider<DashboardProvider>.value(value: mockDashboardProvider),
          ],
          child: const MaterialApp(
            home: MultiStepRegisterScreen(),
          ),
        ),
      );

      // Navigate to step 3 (role selection)
      // Fill step 1
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(2), '**********');
      await tester.tap(find.text('Next'));
      await tester.pumpAndSettle();

      // Fill step 2
      await tester.enterText(find.byType(TextFormField).at(0), 'Password123!');
      await tester.enterText(find.byType(TextFormField).at(1), 'Password123!');
      await tester.tap(find.text('Next'));
      await tester.pumpAndSettle();

      // Verify role selection is displayed
      expect(find.text('Choose your role'), findsOneWidget);
      expect(find.text('Buyer'), findsOneWidget);
      expect(find.text('Seller'), findsOneWidget);
      expect(find.text('Agent'), findsOneWidget);
      expect(find.text('Admin'), findsOneWidget);

      // Test role selection
      await tester.tap(find.text('Seller'));
      await tester.pumpAndSettle();

      // Verify seller is selected (this would require checking the internal state)
      // For now, we just verify the UI responds
      expect(find.text('Seller'), findsOneWidget);
    });
  });

  group('Authentication State Management Tests', () {
    testWidgets('Auth wrapper shows loading state correctly', (WidgetTester tester) async {
      final mockUserProvider = MockUserProvider();
      when(mockUserProvider.isLoading).thenReturn(true);
      when(mockUserProvider.isAuthenticated).thenReturn(false);
      when(mockUserProvider.currentUser).thenReturn(null);

      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider<UserProvider>.value(value: mockUserProvider),
          ],
          child: const MaterialApp(
            home: Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading...'), findsOneWidget);
    });
  });
}
