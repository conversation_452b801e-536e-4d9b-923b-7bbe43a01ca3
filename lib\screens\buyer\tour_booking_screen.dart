import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class TourBookingScreen extends StatefulWidget {
  final String? propertyId;

  const TourBookingScreen({
    Key? key,
    this.propertyId,
  }) : super(key: key);

  @override
  State<TourBookingScreen> createState() => _TourBookingScreenState();
}

class _TourBookingScreenState extends State<TourBookingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // Form controllers
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _messageController = TextEditingController();
  
  // Form state
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  TourType _selectedTourType = TourType.inPerson;
  String _selectedProperty = '';
  bool _isLoading = false;
  
  // Mock data
  List<TourBooking> _upcomingTours = [];
  List<TourBooking> _pastTours = [];
  List<String> _availableProperties = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _availableProperties = [
      'Modern Downtown Condo - 123 Main St',
      'Family Home with Garden - 456 Oak Ave',
      'Waterfront Apartment - 789 Beach Rd',
    ];
    
    if (widget.propertyId != null) {
      _selectedProperty = _availableProperties.first;
    }

    _upcomingTours = [
      TourBooking(
        id: '1',
        propertyTitle: 'Modern Downtown Condo',
        propertyAddress: '123 Main St, Downtown',
        agentName: 'Sarah Agent',
        agentPhone: '+1234567890',
        tourDate: DateTime.now().add(const Duration(days: 2)),
        tourTime: const TimeOfDay(hour: 14, minute: 0),
        tourType: TourType.inPerson,
        status: TourStatus.confirmed,
        notes: 'Interested in the view and amenities',
      ),
      TourBooking(
        id: '2',
        propertyTitle: 'Family Home with Garden',
        propertyAddress: '456 Oak Ave, Suburbs',
        agentName: 'Mike Agent',
        agentPhone: '+1234567891',
        tourDate: DateTime.now().add(const Duration(days: 5)),
        tourTime: const TimeOfDay(hour: 10, minute: 30),
        tourType: TourType.virtual,
        status: TourStatus.pending,
        notes: 'Virtual tour to check layout',
      ),
    ];

    _pastTours = [
      TourBooking(
        id: '3',
        propertyTitle: 'Waterfront Apartment',
        propertyAddress: '789 Beach Rd, Waterfront',
        agentName: 'Lisa Agent',
        agentPhone: '+1234567892',
        tourDate: DateTime.now().subtract(const Duration(days: 3)),
        tourTime: const TimeOfDay(hour: 16, minute: 0),
        tourType: TourType.inPerson,
        status: TourStatus.completed,
        notes: 'Great views but too expensive',
      ),
    ];
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Property Tours'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Book Tour'),
            Tab(text: 'Upcoming'),
            Tab(text: 'Past Tours'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildBookTourTab(),
            _buildUpcomingToursTab(),
            _buildPastToursTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildBookTourTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Property Selection'),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _selectedProperty.isEmpty ? null : _selectedProperty,
            decoration: InputDecoration(
              labelText: 'Select Property',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF4CAF50)),
              ),
            ),
            items: _availableProperties.map((property) {
              return DropdownMenuItem(
                value: property,
                child: Text(property),
              );
            }).toList(),
            onChanged: (value) {
              setState(() => _selectedProperty = value!);
            },
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Tour Type'),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: RadioListTile<TourType>(
                  title: const Text('In-Person'),
                  subtitle: const Text('Visit the property'),
                  value: TourType.inPerson,
                  groupValue: _selectedTourType,
                  onChanged: (value) {
                    setState(() => _selectedTourType = value!);
                  },
                  activeColor: const Color(0xFF4CAF50),
                ),
              ),
              Expanded(
                child: RadioListTile<TourType>(
                  title: const Text('Virtual'),
                  subtitle: const Text('Online video tour'),
                  value: TourType.virtual,
                  groupValue: _selectedTourType,
                  onChanged: (value) {
                    setState(() => _selectedTourType = value!);
                  },
                  activeColor: const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Schedule'),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectDate,
                  icon: const Icon(Icons.calendar_today),
                  label: Text(_selectedDate != null 
                      ? _formatDate(_selectedDate!) 
                      : 'Select Date'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    foregroundColor: const Color(0xFF4CAF50),
                    side: const BorderSide(color: Color(0xFF4CAF50)),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectTime,
                  icon: const Icon(Icons.access_time),
                  label: Text(_selectedTime != null 
                      ? _formatTime(_selectedTime!) 
                      : 'Select Time'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    foregroundColor: const Color(0xFF4CAF50),
                    side: const BorderSide(color: Color(0xFF4CAF50)),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Contact Information'),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _nameController,
            label: 'Full Name',
            hint: 'Enter your full name',
            icon: Icons.person,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _emailController,
            label: 'Email',
            hint: 'Enter your email address',
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _phoneController,
            label: 'Phone Number',
            hint: 'Enter your phone number',
            icon: Icons.phone,
            keyboardType: TextInputType.phone,
          ),
          const SizedBox(height: 24),
          _buildSectionTitle('Additional Notes'),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _messageController,
            label: 'Message (Optional)',
            hint: 'Any specific questions or requirements...',
            icon: Icons.message,
            maxLines: 3,
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _bookTour,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4CAF50),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Book Tour',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingToursTab() {
    if (_upcomingTours.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_available, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No upcoming tours',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Book a tour to see your scheduled visits',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _upcomingTours.length,
      itemBuilder: (context, index) {
        final tour = _upcomingTours[index];
        return _buildTourCard(tour, isUpcoming: true);
      },
    );
  }

  Widget _buildPastToursTab() {
    if (_pastTours.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No past tours',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Your completed tours will appear here',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _pastTours.length,
      itemBuilder: (context, index) {
        final tour = _pastTours[index];
        return _buildTourCard(tour, isUpcoming: false);
      },
    );
  }

  Widget _buildTourCard(TourBooking tour, {required bool isUpcoming}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    tour.propertyTitle,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(tour.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    tour.status.displayName,
                    style: TextStyle(
                      color: _getStatusColor(tour.status),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    tour.propertyAddress,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatDate(tour.tourDate),
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                const SizedBox(width: 16),
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatTime(tour.tourTime),
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  tour.tourType == TourType.inPerson ? Icons.location_city : Icons.videocam,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Text(
                  tour.tourType.displayName,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
                const SizedBox(width: 16),
                Icon(Icons.person, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  tour.agentName,
                  style: TextStyle(color: Colors.grey[600], fontSize: 14),
                ),
              ],
            ),
            if (tour.notes.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Text(
                  tour.notes,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                if (isUpcoming) ...[
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _rescheduleOrCancel(tour),
                      icon: const Icon(Icons.edit),
                      label: const Text('Reschedule'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.orange,
                        side: const BorderSide(color: Colors.orange),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _contactAgent(tour),
                    icon: const Icon(Icons.phone),
                    label: const Text('Contact Agent'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4CAF50),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xFF4CAF50),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
  }) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF4CAF50)),
        ),
      ),
      keyboardType: keyboardType,
      maxLines: maxLines,
    );
  }

  Color _getStatusColor(TourStatus status) {
    switch (status) {
      case TourStatus.confirmed:
        return Colors.green;
      case TourStatus.pending:
        return Colors.orange;
      case TourStatus.cancelled:
        return Colors.red;
      case TourStatus.completed:
        return Colors.blue;
    }
  }

  String _formatDate(DateTime date) {
    return '${_getWeekday(date.weekday)}, ${_getMonth(date.month)} ${date.day}';
  }

  String _formatTime(TimeOfDay time) {
    final hour = time.hour > 12 ? time.hour - 12 : time.hour;
    final period = time.hour >= 12 ? 'PM' : 'AM';
    return '${hour == 0 ? 12 : hour}:${time.minute.toString().padLeft(2, '0')} $period';
  }

  String _getWeekday(int weekday) {
    const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return weekdays[weekday - 1];
  }

  String _getMonth(int month) {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return months[month - 1];
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 90)),
    );
    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 14, minute: 0),
    );
    if (time != null) {
      setState(() => _selectedTime = time);
    }
  }

  Future<void> _bookTour() async {
    if (_selectedProperty.isEmpty || _selectedDate == null || _selectedTime == null ||
        _nameController.text.isEmpty || _emailController.text.isEmpty || _phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Tour booked successfully!'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
        
        // Reset form
        setState(() {
          _selectedProperty = '';
          _selectedDate = null;
          _selectedTime = null;
          _nameController.clear();
          _emailController.clear();
          _phoneController.clear();
          _messageController.clear();
        });
        
        // Switch to upcoming tours tab
        _tabController.animateTo(1);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error booking tour: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _rescheduleOrCancel(TourBooking tour) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Tour Options'),
        content: const Text('What would you like to do with this tour?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Reschedule functionality will be implemented'),
                  backgroundColor: Color(0xFF4CAF50),
                ),
              );
            },
            child: const Text('Reschedule'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Tour cancelled'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Cancel Tour'),
          ),
        ],
      ),
    );
  }

  void _contactAgent(TourBooking tour) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Calling ${tour.agentName}...'),
        backgroundColor: const Color(0xFF4CAF50),
      ),
    );
  }
}

// Tour booking data models
class TourBooking {
  final String id;
  final String propertyTitle;
  final String propertyAddress;
  final String agentName;
  final String agentPhone;
  final DateTime tourDate;
  final TimeOfDay tourTime;
  final TourType tourType;
  final TourStatus status;
  final String notes;

  TourBooking({
    required this.id,
    required this.propertyTitle,
    required this.propertyAddress,
    required this.agentName,
    required this.agentPhone,
    required this.tourDate,
    required this.tourTime,
    required this.tourType,
    required this.status,
    required this.notes,
  });
}

enum TourType {
  inPerson,
  virtual,
}

enum TourStatus {
  confirmed,
  pending,
  cancelled,
  completed,
}

extension TourTypeExtension on TourType {
  String get displayName {
    switch (this) {
      case TourType.inPerson:
        return 'In-Person Tour';
      case TourType.virtual:
        return 'Virtual Tour';
    }
  }
}

extension TourStatusExtension on TourStatus {
  String get displayName {
    switch (this) {
      case TourStatus.confirmed:
        return 'Confirmed';
      case TourStatus.pending:
        return 'Pending';
      case TourStatus.cancelled:
        return 'Cancelled';
      case TourStatus.completed:
        return 'Completed';
    }
  }
}
