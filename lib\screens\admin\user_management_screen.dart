import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user.dart';
import '../../providers/user_provider.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({Key? key}) : super(key: key);

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final _searchController = TextEditingController();
  String _selectedRole = 'all';
  String _selectedStatus = 'all';
  bool _isLoading = false;
  
  // Mock user data
  List<UserData> _users = [];
  List<UserData> _filteredUsers = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _users = [
      UserData(
        id: '1',
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '+****************',
        role: UserRole.buyer,
        status: UserStatus.active,
        joinDate: DateTime.now().subtract(const Duration(days: 30)),
        lastLogin: DateTime.now().subtract(const Duration(hours: 2)),
        propertiesCount: 0,
        transactionsCount: 2,
        isVerified: true,
      ),
      UserData(
        id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        role: UserRole.seller,
        status: UserStatus.active,
        joinDate: DateTime.now().subtract(const Duration(days: 45)),
        lastLogin: DateTime.now().subtract(const Duration(hours: 1)),
        propertiesCount: 3,
        transactionsCount: 1,
        isVerified: true,
      ),
      UserData(
        id: '3',
        name: 'Mike Agent',
        email: '<EMAIL>',
        phone: '+****************',
        role: UserRole.agent,
        status: UserStatus.active,
        joinDate: DateTime.now().subtract(const Duration(days: 120)),
        lastLogin: DateTime.now().subtract(const Duration(minutes: 30)),
        propertiesCount: 15,
        transactionsCount: 8,
        isVerified: true,
      ),
      UserData(
        id: '4',
        name: 'Lisa Brown',
        email: '<EMAIL>',
        phone: '+****************',
        role: UserRole.buyer,
        status: UserStatus.suspended,
        joinDate: DateTime.now().subtract(const Duration(days: 60)),
        lastLogin: DateTime.now().subtract(const Duration(days: 7)),
        propertiesCount: 0,
        transactionsCount: 0,
        isVerified: false,
      ),
      UserData(
        id: '5',
        name: 'Tom Wilson',
        email: '<EMAIL>',
        phone: '+****************',
        role: UserRole.seller,
        status: UserStatus.pending,
        joinDate: DateTime.now().subtract(const Duration(days: 5)),
        lastLogin: DateTime.now().subtract(const Duration(days: 2)),
        propertiesCount: 1,
        transactionsCount: 0,
        isVerified: false,
      ),
    ];
    _filteredUsers = List.from(_users);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Management'),
        backgroundColor: const Color(0xFF9C27B0),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _exportUsers,
            icon: const Icon(Icons.download),
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'bulk_actions',
                child: ListTile(
                  leading: Icon(Icons.checklist),
                  title: Text('Bulk Actions'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'send_notification',
                child: ListTile(
                  leading: Icon(Icons.notifications),
                  title: Text('Send Notification'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'user_analytics',
                child: ListTile(
                  leading: Icon(Icons.analytics),
                  title: Text('User Analytics'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All Users'),
            Tab(text: 'Active'),
            Tab(text: 'Pending'),
            Tab(text: 'Suspended'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildStatsSection(),
            _buildFiltersSection(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildUsersList(_filteredUsers),
                  _buildUsersList(_filteredUsers.where((u) => u.status == UserStatus.active).toList()),
                  _buildUsersList(_filteredUsers.where((u) => u.status == UserStatus.pending).toList()),
                  _buildUsersList(_filteredUsers.where((u) => u.status == UserStatus.suspended).toList()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection() {
    final totalUsers = _users.length;
    final activeUsers = _users.where((u) => u.status == UserStatus.active).length;
    final pendingUsers = _users.where((u) => u.status == UserStatus.pending).length;
    final verifiedUsers = _users.where((u) => u.isVerified).length;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard('Total Users', '$totalUsers', Icons.people, Colors.blue),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Active', '$activeUsers', Icons.check_circle, Colors.green),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Pending', '$pendingUsers', Icons.schedule, Colors.orange),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard('Verified', '$verifiedUsers', Icons.verified, Colors.purple),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search users by name, email, or phone...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF9C27B0)),
              ),
            ),
            onChanged: _filterUsers,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedRole,
                  decoration: InputDecoration(
                    labelText: 'Role',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Roles')),
                    DropdownMenuItem(value: 'buyer', child: Text('Buyers')),
                    DropdownMenuItem(value: 'seller', child: Text('Sellers')),
                    DropdownMenuItem(value: 'agent', child: Text('Agents')),
                    DropdownMenuItem(value: 'admin', child: Text('Admins')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedRole = value!);
                    _filterUsers(_searchController.text);
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: 'Status',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Status')),
                    DropdownMenuItem(value: 'active', child: Text('Active')),
                    DropdownMenuItem(value: 'pending', child: Text('Pending')),
                    DropdownMenuItem(value: 'suspended', child: Text('Suspended')),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedStatus = value!);
                    _filterUsers(_searchController.text);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUsersList(List<UserData> users) {
    if (users.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No users found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: _getRoleColor(user.role).withOpacity(0.1),
                      child: Text(
                        user.name.split(' ').map((e) => e[0]).join(),
                        style: TextStyle(
                          color: _getRoleColor(user.role),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                user.name,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 8),
                              if (user.isVerified)
                                const Icon(
                                  Icons.verified,
                                  size: 16,
                                  color: Colors.blue,
                                ),
                            ],
                          ),
                          Text(
                            user.email,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(user.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        user.status.displayName,
                        style: TextStyle(
                          color: _getStatusColor(user.status),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    _buildUserInfo(Icons.badge, user.role.displayName),
                    const SizedBox(width: 16),
                    _buildUserInfo(Icons.phone, user.phone),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildUserInfo(Icons.calendar_today, 'Joined ${_formatDate(user.joinDate)}'),
                    const SizedBox(width: 16),
                    _buildUserInfo(Icons.access_time, 'Last login ${_formatDate(user.lastLogin)}'),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    _buildUserInfo(Icons.home, '${user.propertiesCount} properties'),
                    const SizedBox(width: 16),
                    _buildUserInfo(Icons.receipt, '${user.transactionsCount} transactions'),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _viewUserDetails(user),
                        icon: const Icon(Icons.visibility),
                        label: const Text('View Details'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF9C27B0),
                          side: const BorderSide(color: Color(0xFF9C27B0)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _manageUser(user),
                        icon: const Icon(Icons.settings),
                        label: const Text('Manage'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF9C27B0),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserInfo(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.buyer:
        return Colors.green;
      case UserRole.seller:
        return Colors.blue;
      case UserRole.agent:
        return Colors.orange;
      case UserRole.admin:
        return Colors.purple;
    }
  }

  Color _getStatusColor(UserStatus status) {
    switch (status) {
      case UserStatus.active:
        return Colors.green;
      case UserStatus.pending:
        return Colors.orange;
      case UserStatus.suspended:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }

  void _filterUsers(String query) {
    setState(() {
      _filteredUsers = _users.where((user) {
        final matchesSearch = query.isEmpty ||
            user.name.toLowerCase().contains(query.toLowerCase()) ||
            user.email.toLowerCase().contains(query.toLowerCase()) ||
            user.phone.contains(query);
        
        final matchesRole = _selectedRole == 'all' ||
            user.role.toString().split('.').last == _selectedRole;
        
        final matchesStatus = _selectedStatus == 'all' ||
            user.status.toString().split('.').last == _selectedStatus;
        
        return matchesSearch && matchesRole && matchesStatus;
      }).toList();
    });
  }

  void _viewUserDetails(UserData user) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for ${user.name}'),
        backgroundColor: const Color(0xFF9C27B0),
      ),
    );
  }

  void _manageUser(UserData user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Manage ${user.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit),
              title: const Text('Edit User'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Edit user functionality will be implemented'),
                    backgroundColor: Color(0xFF9C27B0),
                  ),
                );
              },
            ),
            if (user.status == UserStatus.pending)
              ListTile(
                leading: const Icon(Icons.check_circle, color: Colors.green),
                title: const Text('Approve User'),
                onTap: () {
                  Navigator.pop(context);
                  _updateUserStatus(user, UserStatus.active);
                },
              ),
            if (user.status == UserStatus.active)
              ListTile(
                leading: const Icon(Icons.block, color: Colors.red),
                title: const Text('Suspend User'),
                onTap: () {
                  Navigator.pop(context);
                  _updateUserStatus(user, UserStatus.suspended);
                },
              ),
            if (user.status == UserStatus.suspended)
              ListTile(
                leading: const Icon(Icons.restore, color: Colors.green),
                title: const Text('Reactivate User'),
                onTap: () {
                  Navigator.pop(context);
                  _updateUserStatus(user, UserStatus.active);
                },
              ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete User'),
              onTap: () {
                Navigator.pop(context);
                _showDeleteConfirmation(user);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _updateUserStatus(UserData user, UserStatus newStatus) {
    setState(() {
      final index = _users.indexOf(user);
      _users[index] = user.copyWith(status: newStatus);
      _filteredUsers = List.from(_users);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${user.name} status updated to ${newStatus.displayName}'),
        backgroundColor: const Color(0xFF9C27B0),
      ),
    );
  }

  void _showDeleteConfirmation(UserData user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text('Are you sure you want to delete ${user.name}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _users.remove(user);
                _filteredUsers = List.from(_users);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${user.name} has been deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _exportUsers() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exporting user data...'),
        backgroundColor: Color(0xFF9C27B0),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'bulk_actions':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Bulk actions functionality will be implemented'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'send_notification':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Send notification functionality will be implemented'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'user_analytics':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('User analytics functionality will be implemented'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
    }
  }
}

// User data models
class UserData {
  final String id;
  final String name;
  final String email;
  final String phone;
  final UserRole role;
  final UserStatus status;
  final DateTime joinDate;
  final DateTime lastLogin;
  final int propertiesCount;
  final int transactionsCount;
  final bool isVerified;

  UserData({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.status,
    required this.joinDate,
    required this.lastLogin,
    required this.propertiesCount,
    required this.transactionsCount,
    required this.isVerified,
  });

  UserData copyWith({
    UserStatus? status,
    bool? isVerified,
  }) {
    return UserData(
      id: id,
      name: name,
      email: email,
      phone: phone,
      role: role,
      status: status ?? this.status,
      joinDate: joinDate,
      lastLogin: lastLogin,
      propertiesCount: propertiesCount,
      transactionsCount: transactionsCount,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}

enum UserStatus {
  active,
  pending,
  suspended,
}

extension UserStatusExtension on UserStatus {
  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'Active';
      case UserStatus.pending:
        return 'Pending';
      case UserStatus.suspended:
        return 'Suspended';
    }
  }
}
