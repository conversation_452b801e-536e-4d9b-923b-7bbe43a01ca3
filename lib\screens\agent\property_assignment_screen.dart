import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/property.dart';
import '../../models/user.dart';
import '../../providers/property_provider.dart';
import '../../providers/user_provider.dart';

class PropertyAssignmentScreen extends StatefulWidget {
  const PropertyAssignmentScreen({Key? key}) : super(key: key);

  @override
  State<PropertyAssignmentScreen> createState() => _PropertyAssignmentScreenState();
}

class _PropertyAssignmentScreenState extends State<PropertyAssignmentScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final _searchController = TextEditingController();
  String _selectedFilter = 'all';
  bool _isLoading = false;
  
  // Mock assignment data
  List<PropertyAssignment> _assignments = [];
  List<PropertyAssignment> _filteredAssignments = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _assignments = [
      PropertyAssignment(
        id: '1',
        propertyId: 'prop_1',
        propertyTitle: 'Modern Downtown Condo',
        propertyAddress: '123 Main St, Downtown',
        propertyPrice: 650000,
        clientId: 'client_1',
        clientName: 'John Smith',
        assignmentType: AssignmentType.showing,
        status: AssignmentStatus.scheduled,
        scheduledDate: DateTime.now().add(const Duration(days: 2)),
        notes: 'Client specifically interested in the view and amenities.',
        priority: AssignmentPriority.high,
      ),
      PropertyAssignment(
        id: '2',
        propertyId: 'prop_2',
        propertyTitle: 'Family Home with Garden',
        propertyAddress: '456 Oak Ave, Suburbs',
        propertyPrice: 850000,
        clientId: 'client_2',
        clientName: 'Sarah Johnson',
        assignmentType: AssignmentType.listing,
        status: AssignmentStatus.active,
        scheduledDate: DateTime.now().add(const Duration(days: 1)),
        notes: 'Needs professional photos and staging advice.',
        priority: AssignmentPriority.medium,
      ),
      PropertyAssignment(
        id: '3',
        propertyId: 'prop_3',
        propertyTitle: 'Waterfront Apartment',
        propertyAddress: '789 Beach Rd, Waterfront',
        propertyPrice: 750000,
        clientId: 'client_3',
        clientName: 'Michael Brown',
        assignmentType: AssignmentType.evaluation,
        status: AssignmentStatus.pending,
        scheduledDate: DateTime.now().add(const Duration(days: 5)),
        notes: 'First-time buyer needs market analysis.',
        priority: AssignmentPriority.low,
      ),
    ];
    _filteredAssignments = List.from(_assignments);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Property Assignments'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _createNewAssignment,
            icon: const Icon(Icons.add_task),
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'calendar',
                child: ListTile(
                  leading: Icon(Icons.calendar_today),
                  title: Text('Calendar View'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Schedule'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Showings'),
            Tab(text: 'Listings'),
            Tab(text: 'Evaluations'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildSearchAndFilter(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildAssignmentList(_filteredAssignments),
                  _buildAssignmentList(_filteredAssignments.where((a) => a.assignmentType == AssignmentType.showing).toList()),
                  _buildAssignmentList(_filteredAssignments.where((a) => a.assignmentType == AssignmentType.listing).toList()),
                  _buildAssignmentList(_filteredAssignments.where((a) => a.assignmentType == AssignmentType.evaluation).toList()),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewAssignment,
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.add_task),
        label: const Text('New Assignment'),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search assignments...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF2196F3)),
              ),
            ),
            onChanged: _filterAssignments,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('Status: '),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedFilter,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Status')),
                    DropdownMenuItem(value: 'scheduled', child: Text('Scheduled')),
                    DropdownMenuItem(value: 'active', child: Text('Active')),
                    DropdownMenuItem(value: 'pending', child: Text('Pending')),
                    DropdownMenuItem(value: 'completed', child: Text('Completed')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                    _filterAssignments(_searchController.text);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAssignmentList(List<PropertyAssignment> assignments) {
    if (assignments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No assignments found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: assignments.length,
      itemBuilder: (context, index) {
        final assignment = assignments[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(assignment.priority).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        assignment.priority.displayName,
                        style: TextStyle(
                          color: _getPriorityColor(assignment.priority),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(assignment.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        assignment.status.displayName,
                        style: TextStyle(
                          color: _getStatusColor(assignment.status),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  assignment.propertyTitle,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  assignment.propertyAddress,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.attach_money, size: 16, color: Colors.green),
                    Text(
                      '\$${_formatPrice(assignment.propertyPrice)}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(Icons.person, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      assignment.clientName,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Scheduled: ${_formatDateTime(assignment.scheduledDate)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                if (assignment.notes.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    assignment.notes,
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _viewAssignmentDetails(assignment),
                        icon: const Icon(Icons.visibility),
                        label: const Text('View Details'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: const Color(0xFF2196F3),
                          side: const BorderSide(color: Color(0xFF2196F3)),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _updateAssignmentStatus(assignment),
                        icon: const Icon(Icons.update),
                        label: const Text('Update'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF2196F3),
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getPriorityColor(AssignmentPriority priority) {
    switch (priority) {
      case AssignmentPriority.high:
        return Colors.red;
      case AssignmentPriority.medium:
        return Colors.orange;
      case AssignmentPriority.low:
        return Colors.green;
    }
  }

  Color _getStatusColor(AssignmentStatus status) {
    switch (status) {
      case AssignmentStatus.scheduled:
        return Colors.blue;
      case AssignmentStatus.active:
        return Colors.green;
      case AssignmentStatus.pending:
        return Colors.orange;
      case AssignmentStatus.completed:
        return Colors.purple;
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000000) {
      return '${(price / 1000000).toStringAsFixed(1)}M';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(0)}K';
    }
    return price.toStringAsFixed(0);
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);
    
    if (difference.inDays > 0) {
      return 'In ${difference.inDays} day${difference.inDays == 1 ? '' : 's'}';
    } else if (difference.inDays == 0) {
      return 'Today';
    } else {
      return '${difference.inDays.abs()} day${difference.inDays.abs() == 1 ? '' : 's'} ago';
    }
  }

  void _filterAssignments(String query) {
    setState(() {
      _filteredAssignments = _assignments.where((assignment) {
        final matchesSearch = assignment.propertyTitle.toLowerCase().contains(query.toLowerCase()) ||
                            assignment.clientName.toLowerCase().contains(query.toLowerCase()) ||
                            assignment.propertyAddress.toLowerCase().contains(query.toLowerCase());
        final matchesFilter = _selectedFilter == 'all' ||
                            assignment.status.toString().split('.').last == _selectedFilter;
        return matchesSearch && matchesFilter;
      }).toList();
    });
  }

  void _createNewAssignment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Create new assignment functionality will be implemented'),
        backgroundColor: Color(0xFF2196F3),
      ),
    );
  }

  void _viewAssignmentDetails(PropertyAssignment assignment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for ${assignment.propertyTitle}'),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }

  void _updateAssignmentStatus(PropertyAssignment assignment) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Updating status for ${assignment.propertyTitle}'),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'calendar':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Calendar view will be implemented'),
            backgroundColor: Color(0xFF2196F3),
          ),
        );
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting schedule...'),
            backgroundColor: Color(0xFF2196F3),
          ),
        );
        break;
    }
  }
}

// Assignment data models
class PropertyAssignment {
  final String id;
  final String propertyId;
  final String propertyTitle;
  final String propertyAddress;
  final double propertyPrice;
  final String clientId;
  final String clientName;
  final AssignmentType assignmentType;
  final AssignmentStatus status;
  final DateTime scheduledDate;
  final String notes;
  final AssignmentPriority priority;

  PropertyAssignment({
    required this.id,
    required this.propertyId,
    required this.propertyTitle,
    required this.propertyAddress,
    required this.propertyPrice,
    required this.clientId,
    required this.clientName,
    required this.assignmentType,
    required this.status,
    required this.scheduledDate,
    required this.notes,
    required this.priority,
  });
}

enum AssignmentType {
  showing,
  listing,
  evaluation,
}

enum AssignmentStatus {
  scheduled,
  active,
  pending,
  completed,
}

enum AssignmentPriority {
  high,
  medium,
  low,
}

extension AssignmentStatusExtension on AssignmentStatus {
  String get displayName {
    switch (this) {
      case AssignmentStatus.scheduled:
        return 'Scheduled';
      case AssignmentStatus.active:
        return 'Active';
      case AssignmentStatus.pending:
        return 'Pending';
      case AssignmentStatus.completed:
        return 'Completed';
    }
  }
}

extension AssignmentPriorityExtension on AssignmentPriority {
  String get displayName {
    switch (this) {
      case AssignmentPriority.high:
        return 'High';
      case AssignmentPriority.medium:
        return 'Medium';
      case AssignmentPriority.low:
        return 'Low';
    }
  }
}
