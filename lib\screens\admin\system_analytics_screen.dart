import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';

class SystemAnalyticsScreen extends StatefulWidget {
  const SystemAnalyticsScreen({Key? key}) : super(key: key);

  @override
  State<SystemAnalyticsScreen> createState() => _SystemAnalyticsScreenState();
}

class _SystemAnalyticsScreenState extends State<SystemAnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  String _selectedPeriod = 'month';
  bool _isLoading = false;

  // Mock analytics data
  SystemAnalytics? _analytics;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _analytics = SystemAnalytics(
      totalUsers: 1250,
      activeUsers: 980,
      totalProperties: 3420,
      activeListings: 2150,
      totalTransactions: 450,
      totalRevenue: 2850000,
      monthlyGrowth: 12.5,
      userGrowthData: [
        ChartData('Jan', 850),
        ChartData('Feb', 920),
        ChartData('Mar', 1050),
        ChartData('Apr', 1180),
        ChartData('May', 1250),
      ],
      propertyGrowthData: [
        ChartData('Jan', 2800),
        ChartData('Feb', 3050),
        ChartData('Mar', 3200),
        ChartData('Apr', 3350),
        ChartData('May', 3420),
      ],
      revenueData: [
        ChartData('Jan', 2200000),
        ChartData('Feb', 2350000),
        ChartData('Mar', 2500000),
        ChartData('Apr', 2700000),
        ChartData('May', 2850000),
      ],
      usersByRole: {
        'Buyers': 650,
        'Sellers': 380,
        'Agents': 180,
        'Admins': 40,
      },
      propertiesByType: {
        'Houses': 1450,
        'Apartments': 980,
        'Condos': 720,
        'Townhouses': 270,
      },
      topPerformingAgents: [
        AgentPerformance('Sarah Agent', 45, 2250000),
        AgentPerformance('Mike Agent', 38, 1890000),
        AgentPerformance('Lisa Agent', 32, 1650000),
        AgentPerformance('Tom Agent', 28, 1420000),
        AgentPerformance('Jane Agent', 25, 1280000),
      ],
      systemHealth: SystemHealth(
        serverUptime: 99.8,
        responseTime: 245,
        errorRate: 0.2,
        activeConnections: 1250,
        databaseSize: 2.4,
        storageUsed: 68.5,
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('System Analytics'),
        backgroundColor: const Color(0xFF9C27B0),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export_report',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Report'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'schedule_report',
                child: ListTile(
                  leading: Icon(Icons.schedule),
                  title: Text('Schedule Report'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'refresh_data',
                child: ListTile(
                  leading: Icon(Icons.refresh),
                  title: Text('Refresh Data'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Users'),
            Tab(text: 'Properties'),
            Tab(text: 'System Health'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildPeriodSelector(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildOverviewTab(),
                  _buildUsersTab(),
                  _buildPropertiesTab(),
                  _buildSystemHealthTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Row(
        children: [
          const Text('Period: '),
          const SizedBox(width: 8),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedPeriod,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: 'week', child: Text('This Week')),
                DropdownMenuItem(value: 'month', child: Text('This Month')),
                DropdownMenuItem(value: 'quarter', child: Text('This Quarter')),
                DropdownMenuItem(value: 'year', child: Text('This Year')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPeriod = value!;
                });
                _refreshData();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildKeyMetrics(),
          const SizedBox(height: 24),
          _buildRevenueChart(),
          const SizedBox(height: 24),
          _buildTopPerformers(),
        ],
      ),
    );
  }

  Widget _buildKeyMetrics() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'Total Users',
                '${_analytics!.totalUsers}',
                Icons.people,
                Colors.blue,
                '+${_analytics!.monthlyGrowth.toStringAsFixed(1)}%',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                'Active Users',
                '${_analytics!.activeUsers}',
                Icons.people_alt,
                Colors.green,
                '${((_analytics!.activeUsers / _analytics!.totalUsers) * 100).toStringAsFixed(1)}%',
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'Total Properties',
                '${_analytics!.totalProperties}',
                Icons.home,
                Colors.orange,
                '+8.2%',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                'Total Revenue',
                '\$${_formatAmount(_analytics!.totalRevenue)}',
                Icons.attach_money,
                Colors.purple,
                '+15.3%',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color, String change) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    change,
                    style: const TextStyle(
                      color: Colors.green,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueChart() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Revenue Trend',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May'];
                          if (value.toInt() < months.length) {
                            return Text(months[value.toInt()]);
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text('\$${(value / 1000000).toInt()}M');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _analytics!.revenueData.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.value);
                      }).toList(),
                      isCurved: true,
                      color: const Color(0xFF9C27B0),
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: const Color(0xFF9C27B0).withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopPerformers() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Top Performing Agents',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            ..._analytics!.topPerformingAgents.take(5).map((agent) => _buildAgentItem(agent)),
          ],
        ),
      ),
    );
  }

  Widget _buildAgentItem(AgentPerformance agent) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: const Color(0xFF9C27B0).withOpacity(0.1),
            child: Text(
              agent.name.split(' ').map((e) => e[0]).join(),
              style: const TextStyle(
                color: Color(0xFF9C27B0),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  agent.name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${agent.transactions} transactions',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '\$${_formatAmount(agent.revenue)}',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildUserGrowthChart(),
          const SizedBox(height: 24),
          _buildUserDistribution(),
        ],
      ),
    );
  }

  Widget _buildUserGrowthChart() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'User Growth',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: BarChart(
                BarChartData(
                  alignment: BarChartAlignment.spaceAround,
                  maxY: 1500,
                  barTouchData: BarTouchData(enabled: false),
                  titlesData: FlTitlesData(
                    show: true,
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May'];
                          if (value.toInt() < months.length) {
                            return Text(months[value.toInt()]);
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text('${(value / 1000).toInt()}K');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false),
                  barGroups: _analytics!.userGrowthData.asMap().entries.map((entry) {
                    return BarChartGroupData(
                      x: entry.key,
                      barRods: [
                        BarChartRodData(
                          toY: entry.value.value,
                          color: const Color(0xFF9C27B0),
                          width: 20,
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDistribution() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Users by Role',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: _analytics!.usersByRole.entries.map((entry) {
                    final colors = [Colors.blue, Colors.green, Colors.orange, Colors.purple];
                    final index = _analytics!.usersByRole.keys.toList().indexOf(entry.key);
                    return PieChartSectionData(
                      color: colors[index % colors.length],
                      value: entry.value.toDouble(),
                      title: '${entry.key}\n${entry.value}',
                      radius: 80,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertiesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildPropertyGrowthChart(),
          const SizedBox(height: 24),
          _buildPropertyDistribution(),
        ],
      ),
    );
  }

  Widget _buildPropertyGrowthChart() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Property Listings Growth',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: FlTitlesData(
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May'];
                          if (value.toInt() < months.length) {
                            return Text(months[value.toInt()]);
                          }
                          return const Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          return Text('${(value / 1000).toInt()}K');
                        },
                      ),
                    ),
                    topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _analytics!.propertyGrowthData.asMap().entries.map((entry) {
                        return FlSpot(entry.key.toDouble(), entry.value.value);
                      }).toList(),
                      isCurved: true,
                      color: Colors.orange,
                      barWidth: 3,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.orange.withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyDistribution() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Properties by Type',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            ..._analytics!.propertiesByType.entries.map((entry) => _buildPropertyTypeItem(entry)),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyTypeItem(MapEntry<String, int> entry) {
    final total = _analytics!.propertiesByType.values.reduce((a, b) => a + b);
    final percentage = (entry.value / total * 100).toStringAsFixed(1);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: _getPropertyTypeColor(entry.key),
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              entry.key,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            '${entry.value} ($percentage%)',
            style: TextStyle(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemHealthTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSystemHealthMetrics(),
          const SizedBox(height: 24),
          _buildSystemStatus(),
        ],
      ),
    );
  }

  Widget _buildSystemHealthMetrics() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Health Metrics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildHealthMetric(
                    'Server Uptime',
                    '${_analytics!.systemHealth.serverUptime}%',
                    Icons.cloud_done,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildHealthMetric(
                    'Response Time',
                    '${_analytics!.systemHealth.responseTime}ms',
                    Icons.speed,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildHealthMetric(
                    'Error Rate',
                    '${_analytics!.systemHealth.errorRate}%',
                    Icons.error_outline,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildHealthMetric(
                    'Active Connections',
                    '${_analytics!.systemHealth.activeConnections}',
                    Icons.people,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthMetric(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSystemStatus() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF9C27B0),
              ),
            ),
            const SizedBox(height: 16),
            _buildStatusItem('Database', 'Online', Colors.green),
            _buildStatusItem('File Storage', 'Online', Colors.green),
            _buildStatusItem('Email Service', 'Online', Colors.green),
            _buildStatusItem('Payment Gateway', 'Online', Colors.green),
            _buildStatusItem('Backup System', 'Running', Colors.green),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStorageInfo(
                    'Database Size',
                    '${_analytics!.systemHealth.databaseSize} GB',
                    _analytics!.systemHealth.databaseSize / 10,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStorageInfo(
                    'Storage Used',
                    '${_analytics!.systemHealth.storageUsed}%',
                    _analytics!.systemHealth.storageUsed / 100,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String service, String status, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              service,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(
            status,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageInfo(String title, String value, double progress) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            progress > 0.8 ? Colors.red : progress > 0.6 ? Colors.orange : Colors.green,
          ),
        ),
      ],
    );
  }

  Color _getPropertyTypeColor(String type) {
    switch (type) {
      case 'Houses':
        return Colors.blue;
      case 'Apartments':
        return Colors.green;
      case 'Condos':
        return Colors.orange;
      case 'Townhouses':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    }
    return amount.toStringAsFixed(0);
  }

  void _refreshData() {
    setState(() {
      _isLoading = true;
    });

    // Simulate data refresh
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data refreshed successfully'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
      }
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export_report':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting analytics report...'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'schedule_report':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Schedule report functionality will be implemented'),
            backgroundColor: Color(0xFF9C27B0),
          ),
        );
        break;
      case 'refresh_data':
        _refreshData();
        break;
    }
  }
}

// Analytics data models
class SystemAnalytics {
  final int totalUsers;
  final int activeUsers;
  final int totalProperties;
  final int activeListings;
  final int totalTransactions;
  final double totalRevenue;
  final double monthlyGrowth;
  final List<ChartData> userGrowthData;
  final List<ChartData> propertyGrowthData;
  final List<ChartData> revenueData;
  final Map<String, int> usersByRole;
  final Map<String, int> propertiesByType;
  final List<AgentPerformance> topPerformingAgents;
  final SystemHealth systemHealth;

  SystemAnalytics({
    required this.totalUsers,
    required this.activeUsers,
    required this.totalProperties,
    required this.activeListings,
    required this.totalTransactions,
    required this.totalRevenue,
    required this.monthlyGrowth,
    required this.userGrowthData,
    required this.propertyGrowthData,
    required this.revenueData,
    required this.usersByRole,
    required this.propertiesByType,
    required this.topPerformingAgents,
    required this.systemHealth,
  });
}

class ChartData {
  final String label;
  final double value;

  ChartData(this.label, this.value);
}

class AgentPerformance {
  final String name;
  final int transactions;
  final double revenue;

  AgentPerformance(this.name, this.transactions, this.revenue);
}

class SystemHealth {
  final double serverUptime;
  final int responseTime;
  final double errorRate;
  final int activeConnections;
  final double databaseSize;
  final double storageUsed;

  SystemHealth({
    required this.serverUptime,
    required this.responseTime,
    required this.errorRate,
    required this.activeConnections,
    required this.databaseSize,
    required this.storageUsed,
  });
}