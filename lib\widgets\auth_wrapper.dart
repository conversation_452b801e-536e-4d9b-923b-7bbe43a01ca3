import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../auth/auth_screen.dart';
import '../dashboards/seller_dashboard.dart';
import '../dashboards/buyer_dashboard.dart';
import '../dashboards/agent_dashboard.dart';
import '../dashboards/admin_dashboard.dart';
import '../models/user.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        // Show loading screen while authentication state is being determined
        if (userProvider.isLoading) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'Loading...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        }

        // Show auth screen if not authenticated or no user data
        if (!userProvider.isAuthenticated || userProvider.currentUser == null) {
          return const AuthScreen();
        }

        // User is authenticated, show appropriate dashboard
        final user = userProvider.currentUser!;
        
        switch (user.role) {
          case UserRole.seller:
            return const SellerDashboard();
          case UserRole.buyer:
            return const BuyerDashboard();
          case UserRole.agent:
            return const AgentDashboard();
          case UserRole.admin:
            return const AdminDashboard();
          default:
            return const BuyerDashboard(); // Default fallback
        }
      },
    );
  }
}
