import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../auth/auth_screen.dart';
import '../dashboards/seller_dashboard.dart';
import '../dashboards/buyer_dashboard.dart';
import '../dashboards/agent_dashboard.dart';
import '../dashboards/admin_dashboard.dart';
import '../models/user.dart';
import '../services/navigation_service.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  UserProvider? _lastUserProvider;
  bool _hasInitialized = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<UserProvider>(
      builder: (context, userProvider, child) {
        print('AuthWrapper: isLoading=${userProvider.isLoading}, isAuthenticated=${userProvider.isAuthenticated}, user=${userProvider.currentUser?.name}, role=${userProvider.currentUser?.role}');

        // Handle state changes
        _handleStateChange(userProvider);

        // Show loading screen while authentication state is being determined
        if (userProvider.isLoading) {
          return const Scaffold(
            backgroundColor: Colors.white,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2196F3)),
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Loading...',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        // Show auth screen if not authenticated or no user data
        if (!userProvider.isAuthenticated || userProvider.currentUser == null) {
          print('AuthWrapper: Showing auth screen');
          return const AuthScreen();
        }

        // User is authenticated, show appropriate dashboard
        final user = userProvider.currentUser!;
        print('AuthWrapper: Showing dashboard for role ${user.role}');

        return _buildDashboardForRole(user.role);
      },
    );
  }

  void _handleStateChange(UserProvider userProvider) {
    // Check if this is the first time or if the state has changed
    if (_lastUserProvider == null ||
        _lastUserProvider!.isLoading != userProvider.isLoading ||
        _lastUserProvider!.isAuthenticated != userProvider.isAuthenticated ||
        _lastUserProvider!.currentUser?.id != userProvider.currentUser?.id) {

      _lastUserProvider = userProvider;

      // Only handle navigation after initial load
      if (!userProvider.isLoading && _hasInitialized) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _handleNavigation(userProvider);
        });
      }

      if (!userProvider.isLoading) {
        _hasInitialized = true;
      }
    }
  }

  void _handleNavigation(UserProvider userProvider) {
    // This method can be used for programmatic navigation if needed
    // For now, we let the widget tree handle the display
    print('AuthWrapper: Navigation handled - authenticated: ${userProvider.isAuthenticated}');
  }

  Widget _buildDashboardForRole(UserRole role) {
    switch (role) {
      case UserRole.seller:
        return const SellerDashboard();
      case UserRole.buyer:
        return const BuyerDashboard();
      case UserRole.agent:
        return const AgentDashboard();
      case UserRole.admin:
        return const AdminDashboard();
      default:
        print('AuthWrapper: Unknown role $role, defaulting to BuyerDashboard');
        return const BuyerDashboard(); // Default fallback
    }
  }

  @override
  void dispose() {
    _lastUserProvider = null;
    _hasInitialized = false;
    super.dispose();
  }
}
