import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/property.dart';
import '../../models/user.dart';
import '../../providers/property_provider.dart';
import '../../providers/user_provider.dart';

class SavedPropertiesScreen extends StatefulWidget {
  const SavedPropertiesScreen({Key? key}) : super(key: key);

  @override
  State<SavedPropertiesScreen> createState() => _SavedPropertiesScreenState();
}

class _SavedPropertiesScreenState extends State<SavedPropertiesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final _searchController = TextEditingController();
  String _sortBy = 'date_added';
  bool _isLoading = false;
  
  // Mock saved properties data
  List<SavedProperty> _savedProperties = [];
  List<SavedProperty> _filteredProperties = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _savedProperties = [
      SavedProperty(
        property: Property(
          id: '1',
          title: 'Modern Downtown Condo',
          description: 'Beautiful modern condo with city views and premium amenities.',
          price: 650000,
          location: 'Downtown, NY',
          address: '123 Main St',
          type: PropertyType.condo,
          status: PropertyStatus.available,
          bedrooms: 2,
          bathrooms: 2,
          area: 1200,
          images: [],
          latitude: 40.7128,
          longitude: -74.0060,
          agentName: 'Sarah Agent',
          agentPhone: '+1234567890',
          agentEmail: '<EMAIL>',
          amenities: ['Swimming Pool', 'Gym', 'Concierge'],
          ownerId: 'owner1',
          ownerName: 'John Owner',
          ownerEmail: '<EMAIL>',
          ownerPhone: '+1234567890',
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
        savedDate: DateTime.now().subtract(const Duration(days: 2)),
        notes: 'Love the city view and modern amenities. Need to check parking availability.',
        priceAlerts: true,
        category: SavedPropertyCategory.favorites,
      ),
      SavedProperty(
        property: Property(
          id: '2',
          title: 'Family Home with Garden',
          description: 'Spacious family home with large garden and garage.',
          price: 850000,
          location: 'Suburbs, NY',
          address: '456 Oak Ave',
          type: PropertyType.house,
          status: PropertyStatus.available,
          bedrooms: 4,
          bathrooms: 3,
          area: 2500,
          images: [],
          latitude: 40.7589,
          longitude: -73.9851,
          agentName: 'Mike Agent',
          agentPhone: '+1234567891',
          agentEmail: '<EMAIL>',
          amenities: ['Garden', 'Garage', 'Fireplace'],
          ownerId: 'owner2',
          ownerName: 'Jane Owner',
          ownerEmail: '<EMAIL>',
          ownerPhone: '+1234567891',
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
          updatedAt: DateTime.now().subtract(const Duration(days: 2)),
        ),
        savedDate: DateTime.now().subtract(const Duration(days: 5)),
        notes: 'Perfect for family. Great school district. Need to schedule viewing.',
        priceAlerts: true,
        category: SavedPropertyCategory.shortlist,
      ),
      SavedProperty(
        property: Property(
          id: '3',
          title: 'Waterfront Apartment',
          description: 'Stunning waterfront apartment with panoramic views.',
          price: 750000,
          location: 'Waterfront, NY',
          address: '789 Beach Rd',
          type: PropertyType.apartment,
          status: PropertyStatus.pending,
          bedrooms: 3,
          bathrooms: 2,
          area: 1800,
          images: [],
          latitude: 40.6892,
          longitude: -74.0445,
          agentName: 'Lisa Agent',
          agentPhone: '+1234567892',
          agentEmail: '<EMAIL>',
          amenities: ['Balcony', 'Swimming Pool', 'Parking'],
          ownerId: 'owner3',
          ownerName: 'Bob Owner',
          ownerEmail: '<EMAIL>',
          ownerPhone: '+1234567892',
          createdAt: DateTime.now().subtract(const Duration(days: 3)),
          updatedAt: DateTime.now(),
        ),
        savedDate: DateTime.now().subtract(const Duration(days: 1)),
        notes: 'Amazing views but might be over budget. Waiting for price update.',
        priceAlerts: true,
        category: SavedPropertyCategory.watchlist,
      ),
    ];
    _filteredProperties = List.from(_savedProperties);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Saved Properties'),
        backgroundColor: const Color(0xFF4CAF50),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'sort_price_low',
                child: ListTile(
                  leading: Icon(Icons.sort),
                  title: Text('Sort by Price (Low to High)'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'sort_price_high',
                child: ListTile(
                  leading: Icon(Icons.sort),
                  title: Text('Sort by Price (High to Low)'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'sort_date',
                child: ListTile(
                  leading: Icon(Icons.date_range),
                  title: Text('Sort by Date Added'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export List'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All Saved'),
            Tab(text: 'Favorites'),
            Tab(text: 'Shortlist'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildSearchAndStats(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPropertyList(_filteredProperties),
                  _buildPropertyList(_filteredProperties.where((p) => p.category == SavedPropertyCategory.favorites).toList()),
                  _buildPropertyList(_filteredProperties.where((p) => p.category == SavedPropertyCategory.shortlist).toList()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndStats() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search saved properties...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF4CAF50)),
              ),
            ),
            onChanged: _filterProperties,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Total Saved',
                  '${_savedProperties.length}',
                  Icons.bookmark,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Price Alerts',
                  '${_savedProperties.where((p) => p.priceAlerts).length}',
                  Icons.notifications,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Avg Price',
                  '\$${_formatPrice(_calculateAveragePrice())}',
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyList(List<SavedProperty> properties) {
    if (properties.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No saved properties',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Start saving properties you\'re interested in',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: properties.length,
      itemBuilder: (context, index) {
        final savedProperty = properties[index];
        final property = savedProperty.property;
        
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          elevation: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  Container(
                    height: 200,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
                    ),
                    child: property.images.isNotEmpty
                        ? Image.network(
                            property.images.first,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => _buildPlaceholderImage(),
                          )
                        : _buildPlaceholderImage(),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getCategoryColor(savedProperty.category).withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        savedProperty.category.displayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    left: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(property.status).withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        property.status.displayName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            property.title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        PopupMenuButton(
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'view',
                              child: ListTile(
                                leading: Icon(Icons.visibility),
                                title: Text('View Details'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'schedule',
                              child: ListTile(
                                leading: Icon(Icons.calendar_today),
                                title: Text('Schedule Viewing'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'move',
                              child: ListTile(
                                leading: Icon(Icons.move_to_inbox),
                                title: Text('Move to Category'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'remove',
                              child: ListTile(
                                leading: Icon(Icons.delete, color: Colors.red),
                                title: Text('Remove', style: TextStyle(color: Colors.red)),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                          ],
                          onSelected: (value) => _handlePropertyAction(value, savedProperty),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            property.location,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ),
                        if (savedProperty.priceAlerts)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.notifications, size: 12, color: Colors.orange),
                                const SizedBox(width: 2),
                                Text(
                                  'Alert',
                                  style: TextStyle(
                                    color: Colors.orange,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '\$${_formatPrice(property.price)}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4CAF50),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildPropertyFeature(Icons.bed, '${property.bedrooms} bed'),
                        const SizedBox(width: 16),
                        _buildPropertyFeature(Icons.bathtub, '${property.bathrooms} bath'),
                        const SizedBox(width: 16),
                        _buildPropertyFeature(Icons.square_foot, '${property.area.toInt()} sqft'),
                      ],
                    ),
                    if (savedProperty.notes.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.note, size: 16, color: Colors.grey[600]),
                                const SizedBox(width: 4),
                                Text(
                                  'Notes',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              savedProperty.notes,
                              style: const TextStyle(fontSize: 14),
                            ),
                          ],
                        ),
                      ),
                    ],
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Text(
                          'Saved ${_formatDate(savedProperty.savedDate)}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        const Spacer(),
                        Text(
                          'Agent: ${property.agentName}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => _scheduleViewing(property),
                            icon: const Icon(Icons.calendar_today),
                            label: const Text('Schedule Viewing'),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: const Color(0xFF4CAF50),
                              side: const BorderSide(color: Color(0xFF4CAF50)),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => _viewPropertyDetails(property),
                            icon: const Icon(Icons.visibility),
                            label: const Text('View Details'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF4CAF50),
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: const BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: const Center(
        child: Icon(
          Icons.home,
          size: 60,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildPropertyFeature(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Color _getCategoryColor(SavedPropertyCategory category) {
    switch (category) {
      case SavedPropertyCategory.favorites:
        return Colors.red;
      case SavedPropertyCategory.shortlist:
        return Colors.blue;
      case SavedPropertyCategory.watchlist:
        return Colors.orange;
    }
  }

  Color _getStatusColor(PropertyStatus status) {
    switch (status) {
      case PropertyStatus.available:
        return Colors.green;
      case PropertyStatus.pending:
        return Colors.orange;
      case PropertyStatus.sold:
        return Colors.red;
      case PropertyStatus.rented:
        return Colors.purple;
      case PropertyStatus.active:
        return Colors.blue;
      case PropertyStatus.inactive:
        return Colors.grey;
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000000) {
      return '${(price / 1000000).toStringAsFixed(1)}M';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(0)}K';
    }
    return price.toStringAsFixed(0);
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  double _calculateAveragePrice() {
    if (_savedProperties.isEmpty) return 0;
    final total = _savedProperties.map((p) => p.property.price).reduce((a, b) => a + b);
    return total / _savedProperties.length;
  }

  void _filterProperties(String query) {
    setState(() {
      _filteredProperties = _savedProperties.where((savedProperty) {
        final property = savedProperty.property;
        return property.title.toLowerCase().contains(query.toLowerCase()) ||
               property.location.toLowerCase().contains(query.toLowerCase()) ||
               savedProperty.notes.toLowerCase().contains(query.toLowerCase());
      }).toList();
    });
  }

  void _scheduleViewing(Property property) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Scheduling viewing for ${property.title}'),
        backgroundColor: const Color(0xFF4CAF50),
      ),
    );
  }

  void _viewPropertyDetails(Property property) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening details for ${property.title}'),
        backgroundColor: const Color(0xFF4CAF50),
      ),
    );
  }

  void _handlePropertyAction(String action, SavedProperty savedProperty) {
    switch (action) {
      case 'view':
        _viewPropertyDetails(savedProperty.property);
        break;
      case 'schedule':
        _scheduleViewing(savedProperty.property);
        break;
      case 'move':
        _showMoveToDialog(savedProperty);
        break;
      case 'remove':
        _removeSavedProperty(savedProperty);
        break;
    }
  }

  void _showMoveToDialog(SavedProperty savedProperty) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Move to Category'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: SavedPropertyCategory.values.map((category) {
            return ListTile(
              title: Text(category.displayName),
              leading: Icon(
                _getCategoryIcon(category),
                color: _getCategoryColor(category),
              ),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  final index = _savedProperties.indexOf(savedProperty);
                  _savedProperties[index] = savedProperty.copyWith(category: category);
                  _filteredProperties = List.from(_savedProperties);
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Moved to ${category.displayName}'),
                    backgroundColor: const Color(0xFF4CAF50),
                  ),
                );
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  IconData _getCategoryIcon(SavedPropertyCategory category) {
    switch (category) {
      case SavedPropertyCategory.favorites:
        return Icons.favorite;
      case SavedPropertyCategory.shortlist:
        return Icons.list;
      case SavedPropertyCategory.watchlist:
        return Icons.visibility;
    }
  }

  void _removeSavedProperty(SavedProperty savedProperty) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Property'),
        content: Text('Are you sure you want to remove "${savedProperty.property.title}" from your saved properties?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _savedProperties.remove(savedProperty);
                _filteredProperties = List.from(_savedProperties);
              });
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Property removed from saved list'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'sort_price_low':
        setState(() {
          _filteredProperties.sort((a, b) => a.property.price.compareTo(b.property.price));
        });
        break;
      case 'sort_price_high':
        setState(() {
          _filteredProperties.sort((a, b) => b.property.price.compareTo(a.property.price));
        });
        break;
      case 'sort_date':
        setState(() {
          _filteredProperties.sort((a, b) => b.savedDate.compareTo(a.savedDate));
        });
        break;
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting saved properties list...'),
            backgroundColor: Color(0xFF4CAF50),
          ),
        );
        break;
    }
  }
}

// Saved property data models
class SavedProperty {
  final Property property;
  final DateTime savedDate;
  final String notes;
  final bool priceAlerts;
  final SavedPropertyCategory category;

  SavedProperty({
    required this.property,
    required this.savedDate,
    required this.notes,
    required this.priceAlerts,
    required this.category,
  });

  SavedProperty copyWith({
    Property? property,
    DateTime? savedDate,
    String? notes,
    bool? priceAlerts,
    SavedPropertyCategory? category,
  }) {
    return SavedProperty(
      property: property ?? this.property,
      savedDate: savedDate ?? this.savedDate,
      notes: notes ?? this.notes,
      priceAlerts: priceAlerts ?? this.priceAlerts,
      category: category ?? this.category,
    );
  }
}

enum SavedPropertyCategory {
  favorites,
  shortlist,
  watchlist,
}

extension SavedPropertyCategoryExtension on SavedPropertyCategory {
  String get displayName {
    switch (this) {
      case SavedPropertyCategory.favorites:
        return 'Favorites';
      case SavedPropertyCategory.shortlist:
        return 'Shortlist';
      case SavedPropertyCategory.watchlist:
        return 'Watchlist';
    }
  }
}
