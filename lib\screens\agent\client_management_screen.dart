import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user.dart';
import '../../providers/user_provider.dart';
import '../../providers/dashboard_provider.dart';

class ClientManagementScreen extends StatefulWidget {
  const ClientManagementScreen({Key? key}) : super(key: key);

  @override
  State<ClientManagementScreen> createState() => _ClientManagementScreenState();
}

class _ClientManagementScreenState extends State<ClientManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  final _searchController = TextEditingController();
  String _selectedFilter = 'all';
  bool _isLoading = false;
  
  // Mock client data - in real app, this would be fetched from the service
  List<ClientData> _clients = [];
  List<ClientData> _filteredClients = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _clients = [
      ClientData(
        id: '1',
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '+****************',
        status: ClientStatus.active,
        type: ClientType.buyer,
        budget: 750000,
        preferredLocations: ['Downtown', 'Suburbs'],
        assignedDate: DateTime.now().subtract(const Duration(days: 30)),
        lastContact: DateTime.now().subtract(const Duration(days: 2)),
        notes: 'Looking for a family home with good schools nearby.',
      ),
      ClientData(
        id: '2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        status: ClientStatus.active,
        type: ClientType.seller,
        budget: 0,
        preferredLocations: ['City Center'],
        assignedDate: DateTime.now().subtract(const Duration(days: 15)),
        lastContact: DateTime.now().subtract(const Duration(days: 1)),
        notes: 'Wants to sell quickly due to job relocation.',
      ),
      ClientData(
        id: '3',
        name: 'Michael Brown',
        email: '<EMAIL>',
        phone: '+****************',
        status: ClientStatus.potential,
        type: ClientType.buyer,
        budget: 500000,
        preferredLocations: ['Waterfront'],
        assignedDate: DateTime.now().subtract(const Duration(days: 7)),
        lastContact: DateTime.now().subtract(const Duration(days: 5)),
        notes: 'First-time buyer, needs guidance through the process.',
      ),
    ];
    _filteredClients = List.from(_clients);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Client Management'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _addNewClient,
            icon: const Icon(Icons.person_add),
          ),
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Clients'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'import',
                child: ListTile(
                  leading: Icon(Icons.upload),
                  title: Text('Import Clients'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'All Clients'),
            Tab(text: 'Active'),
            Tab(text: 'Potential'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildSearchAndFilter(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildClientList(_filteredClients),
                  _buildClientList(_filteredClients.where((c) => c.status == ClientStatus.active).toList()),
                  _buildClientList(_filteredClients.where((c) => c.status == ClientStatus.potential).toList()),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addNewClient,
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        icon: const Icon(Icons.person_add),
        label: const Text('Add Client'),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search clients...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Color(0xFF2196F3)),
              ),
            ),
            onChanged: _filterClients,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Text('Filter by type: '),
              const SizedBox(width: 8),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedFilter,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'all', child: Text('All Types')),
                    DropdownMenuItem(value: 'buyer', child: Text('Buyers')),
                    DropdownMenuItem(value: 'seller', child: Text('Sellers')),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value!;
                    });
                    _filterClients(_searchController.text);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildClientList(List<ClientData> clients) {
    if (clients.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No clients found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: clients.length,
      itemBuilder: (context, index) {
        final client = clients[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getClientTypeColor(client.type).withOpacity(0.1),
              child: Icon(
                client.type == ClientType.buyer ? Icons.home_outlined : Icons.sell_outlined,
                color: _getClientTypeColor(client.type),
              ),
            ),
            title: Text(
              client.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(client.email),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: _getStatusColor(client.status).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        client.status.displayName,
                        style: TextStyle(
                          color: _getStatusColor(client.status),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Last contact: ${_formatDate(client.lastContact)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing: PopupMenuButton(
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'view',
                  child: ListTile(
                    leading: Icon(Icons.visibility),
                    title: Text('View Details'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'contact',
                  child: ListTile(
                    leading: Icon(Icons.phone),
                    title: Text('Contact'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('Edit'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
              onSelected: (value) => _handleClientAction(value, client),
            ),
            onTap: () => _viewClientDetails(client),
          ),
        );
      },
    );
  }

  Color _getClientTypeColor(ClientType type) {
    switch (type) {
      case ClientType.buyer:
        return Colors.green;
      case ClientType.seller:
        return Colors.orange;
    }
  }

  Color _getStatusColor(ClientStatus status) {
    switch (status) {
      case ClientStatus.active:
        return Colors.green;
      case ClientStatus.potential:
        return Colors.orange;
      case ClientStatus.inactive:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }

  void _filterClients(String query) {
    setState(() {
      _filteredClients = _clients.where((client) {
        final matchesSearch = client.name.toLowerCase().contains(query.toLowerCase()) ||
                            client.email.toLowerCase().contains(query.toLowerCase());
        final matchesFilter = _selectedFilter == 'all' ||
                            (_selectedFilter == 'buyer' && client.type == ClientType.buyer) ||
                            (_selectedFilter == 'seller' && client.type == ClientType.seller);
        return matchesSearch && matchesFilter;
      }).toList();
    });
  }

  void _addNewClient() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Add new client functionality will be implemented'),
        backgroundColor: Color(0xFF2196F3),
      ),
    );
  }

  void _viewClientDetails(ClientData client) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing details for ${client.name}'),
        backgroundColor: const Color(0xFF2196F3),
      ),
    );
  }

  void _handleClientAction(String action, ClientData client) {
    switch (action) {
      case 'view':
        _viewClientDetails(client);
        break;
      case 'contact':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Contacting ${client.name}...'),
            backgroundColor: const Color(0xFF2196F3),
          ),
        );
        break;
      case 'edit':
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Editing ${client.name}...'),
            backgroundColor: const Color(0xFF2196F3),
          ),
        );
        break;
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting client data...'),
            backgroundColor: Color(0xFF2196F3),
          ),
        );
        break;
      case 'import':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Import functionality will be implemented'),
            backgroundColor: Color(0xFF2196F3),
          ),
        );
        break;
    }
  }
}

// Client data models
class ClientData {
  final String id;
  final String name;
  final String email;
  final String phone;
  final ClientStatus status;
  final ClientType type;
  final double budget;
  final List<String> preferredLocations;
  final DateTime assignedDate;
  final DateTime lastContact;
  final String notes;

  ClientData({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.status,
    required this.type,
    required this.budget,
    required this.preferredLocations,
    required this.assignedDate,
    required this.lastContact,
    required this.notes,
  });
}

enum ClientStatus {
  active,
  potential,
  inactive,
}

enum ClientType {
  buyer,
  seller,
}

extension ClientStatusExtension on ClientStatus {
  String get displayName {
    switch (this) {
      case ClientStatus.active:
        return 'Active';
      case ClientStatus.potential:
        return 'Potential';
      case ClientStatus.inactive:
        return 'Inactive';
    }
  }
}
