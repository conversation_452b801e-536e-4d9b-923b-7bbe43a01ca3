import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';

class CommissionDetailsScreen extends StatefulWidget {
  const CommissionDetailsScreen({Key? key}) : super(key: key);

  @override
  State<CommissionDetailsScreen> createState() => _CommissionDetailsScreenState();
}

class _CommissionDetailsScreenState extends State<CommissionDetailsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  String _selectedPeriod = 'month';
  bool _isLoading = false;
  
  // Mock commission data
  List<CommissionData> _commissions = [];
  CommissionSummary? _summary;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _initializeMockData();
    _animationController.forward();
  }

  void _initializeMockData() {
    _commissions = [
      CommissionData(
        id: '1',
        propertyId: 'prop_1',
        propertyTitle: 'Modern Downtown Condo',
        propertyAddress: '123 Main St, Downtown',
        salePrice: 650000,
        commissionRate: 0.06,
        commissionAmount: 39000,
        clientName: 'John Smith',
        saleDate: DateTime.now().subtract(const Duration(days: 15)),
        status: CommissionStatus.paid,
        paymentDate: DateTime.now().subtract(const Duration(days: 10)),
        splitWith: ['Sarah Agent'],
        splitAmount: 19500,
      ),
      CommissionData(
        id: '2',
        propertyId: 'prop_2',
        propertyTitle: 'Family Home with Garden',
        propertyAddress: '456 Oak Ave, Suburbs',
        salePrice: 850000,
        commissionRate: 0.05,
        commissionAmount: 42500,
        clientName: 'Sarah Johnson',
        saleDate: DateTime.now().subtract(const Duration(days: 30)),
        status: CommissionStatus.pending,
        paymentDate: null,
        splitWith: [],
        splitAmount: 42500,
      ),
      CommissionData(
        id: '3',
        propertyId: 'prop_3',
        propertyTitle: 'Waterfront Apartment',
        propertyAddress: '789 Beach Rd, Waterfront',
        salePrice: 750000,
        commissionRate: 0.055,
        commissionAmount: 41250,
        clientName: 'Michael Brown',
        saleDate: DateTime.now().subtract(const Duration(days: 45)),
        status: CommissionStatus.processing,
        paymentDate: null,
        splitWith: ['Tom Agent', 'Lisa Agent'],
        splitAmount: 13750,
      ),
    ];

    _summary = CommissionSummary(
      totalEarned: 122750,
      totalPending: 83750,
      totalPaid: 39000,
      averageCommission: 40916.67,
      totalTransactions: 3,
      monthlyEarnings: [
        MonthlyEarning(month: 'Jan', amount: 25000),
        MonthlyEarning(month: 'Feb', amount: 32000),
        MonthlyEarning(month: 'Mar', amount: 28000),
        MonthlyEarning(month: 'Apr', amount: 37750),
      ],
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Commission Details'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          PopupMenuButton(
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text('Export Report'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'tax_report',
                child: ListTile(
                  leading: Icon(Icons.receipt),
                  title: Text('Tax Report'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
            onSelected: _handleMenuAction,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Transactions'),
            Tab(text: 'Analytics'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildOverviewTab(),
            _buildTransactionsTab(),
            _buildAnalyticsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCards(),
          const SizedBox(height: 24),
          _buildRecentTransactions(),
          const SizedBox(height: 24),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Earned',
                '\$${_formatAmount(_summary!.totalEarned)}',
                Icons.account_balance_wallet,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Pending',
                '\$${_formatAmount(_summary!.totalPending)}',
                Icons.schedule,
                Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Paid',
                '\$${_formatAmount(_summary!.totalPaid)}',
                Icons.check_circle,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard(
                'Avg Commission',
                '\$${_formatAmount(_summary!.averageCommission)}',
                Icons.trending_up,
                Colors.purple,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Transactions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2196F3),
              ),
            ),
            const SizedBox(height: 16),
            ..._commissions.take(3).map((commission) => _buildTransactionItem(commission)),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => _tabController.animateTo(1),
              child: const Text('View All Transactions'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(CommissionData commission) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getStatusColor(commission.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.home,
              color: _getStatusColor(commission.status),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  commission.propertyTitle,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  commission.clientName,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '\$${_formatAmount(commission.commissionAmount)}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _getStatusColor(commission.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  commission.status.displayName,
                  style: TextStyle(
                    color: _getStatusColor(commission.status),
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2196F3),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _requestPayment,
                    icon: const Icon(Icons.payment),
                    label: const Text('Request Payment'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2196F3),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _generateReport,
                    icon: const Icon(Icons.description),
                    label: const Text('Generate Report'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: const Color(0xFF2196F3),
                      side: const BorderSide(color: Color(0xFF2196F3)),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsTab() {
    return Column(
      children: [
        _buildFilterSection(),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _commissions.length,
            itemBuilder: (context, index) {
              final commission = _commissions[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              commission.propertyTitle,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(commission.status).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              commission.status.displayName,
                              style: TextStyle(
                                color: _getStatusColor(commission.status),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        commission.propertyAddress,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          _buildDetailChip('Sale Price', '\$${_formatAmount(commission.salePrice)}'),
                          const SizedBox(width: 8),
                          _buildDetailChip('Rate', '${(commission.commissionRate * 100).toStringAsFixed(1)}%'),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          _buildDetailChip('Commission', '\$${_formatAmount(commission.commissionAmount)}'),
                          const SizedBox(width: 8),
                          _buildDetailChip('Your Share', '\$${_formatAmount(commission.splitAmount)}'),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(Icons.person, size: 16, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                            commission.clientName,
                            style: TextStyle(color: Colors.grey[600], fontSize: 14),
                          ),
                          const Spacer(),
                          Text(
                            _formatDate(commission.saleDate),
                            style: TextStyle(color: Colors.grey[600], fontSize: 14),
                          ),
                        ],
                      ),
                      if (commission.splitWith.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Split with: ${commission.splitWith.join(', ')}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFilterSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[50],
      child: Row(
        children: [
          const Text('Filter by: '),
          const SizedBox(width: 8),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedPeriod,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: const [
                DropdownMenuItem(value: 'month', child: Text('This Month')),
                DropdownMenuItem(value: 'quarter', child: Text('This Quarter')),
                DropdownMenuItem(value: 'year', child: Text('This Year')),
                DropdownMenuItem(value: 'all', child: Text('All Time')),
              ],
              onChanged: (value) {
                setState(() {
                  _selectedPeriod = value!;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailChip(String label, String value) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        '$label: $value',
        style: const TextStyle(fontSize: 12),
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Monthly Earnings',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2196F3),
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: 40000,
                barTouchData: BarTouchData(enabled: false),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        final months = ['Jan', 'Feb', 'Mar', 'Apr'];
                        if (value.toInt() < months.length) {
                          return Text(months[value.toInt()]);
                        }
                        return const Text('');
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        return Text('\$${(value / 1000).toInt()}K');
                      },
                    ),
                  ),
                  topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _summary!.monthlyEarnings.asMap().entries.map((entry) {
                  return BarChartGroupData(
                    x: entry.key,
                    barRods: [
                      BarChartRodData(
                        toY: entry.value.amount,
                        color: const Color(0xFF2196F3),
                        width: 20,
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(4)),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
          const SizedBox(height: 24),
          _buildPerformanceMetrics(),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance Metrics',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2196F3),
              ),
            ),
            const SizedBox(height: 16),
            _buildMetricRow('Total Transactions', '${_summary!.totalTransactions}'),
            _buildMetricRow('Average Commission Rate', '${(_commissions.map((c) => c.commissionRate).reduce((a, b) => a + b) / _commissions.length * 100).toStringAsFixed(1)}%'),
            _buildMetricRow('Highest Commission', '\$${_formatAmount(_commissions.map((c) => c.commissionAmount).reduce((a, b) => a > b ? a : b))}'),
            _buildMetricRow('Commission Conversion Rate', '100%'),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[700],
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(CommissionStatus status) {
    switch (status) {
      case CommissionStatus.paid:
        return Colors.green;
      case CommissionStatus.pending:
        return Colors.orange;
      case CommissionStatus.processing:
        return Colors.blue;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    }
    return amount.toStringAsFixed(0);
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _requestPayment() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Payment request functionality will be implemented'),
        backgroundColor: Color(0xFF2196F3),
      ),
    );
  }

  void _generateReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Generating commission report...'),
        backgroundColor: Color(0xFF2196F3),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exporting commission report...'),
            backgroundColor: Color(0xFF2196F3),
          ),
        );
        break;
      case 'tax_report':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Generating tax report...'),
            backgroundColor: Color(0xFF2196F3),
          ),
        );
        break;
    }
  }
}

// Commission data models
class CommissionData {
  final String id;
  final String propertyId;
  final String propertyTitle;
  final String propertyAddress;
  final double salePrice;
  final double commissionRate;
  final double commissionAmount;
  final String clientName;
  final DateTime saleDate;
  final CommissionStatus status;
  final DateTime? paymentDate;
  final List<String> splitWith;
  final double splitAmount;

  CommissionData({
    required this.id,
    required this.propertyId,
    required this.propertyTitle,
    required this.propertyAddress,
    required this.salePrice,
    required this.commissionRate,
    required this.commissionAmount,
    required this.clientName,
    required this.saleDate,
    required this.status,
    this.paymentDate,
    required this.splitWith,
    required this.splitAmount,
  });
}

class CommissionSummary {
  final double totalEarned;
  final double totalPending;
  final double totalPaid;
  final double averageCommission;
  final int totalTransactions;
  final List<MonthlyEarning> monthlyEarnings;

  CommissionSummary({
    required this.totalEarned,
    required this.totalPending,
    required this.totalPaid,
    required this.averageCommission,
    required this.totalTransactions,
    required this.monthlyEarnings,
  });
}

class MonthlyEarning {
  final String month;
  final double amount;

  MonthlyEarning({
    required this.month,
    required this.amount,
  });
}

enum CommissionStatus {
  paid,
  pending,
  processing,
}

extension CommissionStatusExtension on CommissionStatus {
  String get displayName {
    switch (this) {
      case CommissionStatus.paid:
        return 'Paid';
      case CommissionStatus.pending:
        return 'Pending';
      case CommissionStatus.processing:
        return 'Processing';
    }
  }
}
